#!/usr/bin/env python3
"""
واجهة سطر الأوامر لخوارزمية التشفير المتقدمة
Command Line Interface for Advanced Quantum-Resistant Encryption Algorithm
"""

import argparse
import sys
import os
import getpass
import json
from advanced_crypto import QuantumResistantCrypto


def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🔐 خوارزمية التشفير المتقدمة                    ║
║              Advanced Quantum-Resistant Encryption           ║
║                                                              ║
║  🛡️  تشفير متعدد الطبقات مع مقاومة الحوسبة الكمية                ║
║  🚀 AES-256-GCM + RSA-4096 + XOR متعدد المستويات              ║
║  ✅ التحقق من السلامة مع HMAC-SHA3-512                        ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def encrypt_command(args):
    """تنفيذ أمر التشفير"""
    crypto = QuantumResistantCrypto()
    
    try:
        # التحقق من وجود الملف
        if not os.path.exists(args.input):
            print(f"❌ الملف غير موجود: {args.input}")
            return False
        
        # الحصول على كلمة المرور
        if args.password:
            password = args.password
        else:
            password = getpass.getpass("🔑 أدخل كلمة المرور: ")
            confirm_password = getpass.getpass("🔑 تأكيد كلمة المرور: ")
            
            if password != confirm_password:
                print("❌ كلمات المرور غير متطابقة!")
                return False
        
        # قراءة المفتاح العام إذا تم تحديده
        public_key_pem = None
        if args.public_key:
            if not os.path.exists(args.public_key):
                print(f"❌ ملف المفتاح العام غير موجود: {args.public_key}")
                return False
            
            with open(args.public_key, 'rb') as f:
                public_key_pem = f.read()
        
        # تحديد ملف الإخراج
        output_file = args.output or f"{args.input}.encrypted"
        
        print(f"🔒 جاري تشفير الملف: {args.input}")
        
        # تشفير الملف
        if args.data_mode:
            # تشفير البيانات مباشرة
            with open(args.input, 'rb') as f:
                data = f.read()
            
            encrypted_package = crypto.encrypt_data(data, password, public_key_pem)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(encrypted_package, f, indent=2, ensure_ascii=False)
        else:
            # تشفير الملف
            output_file = crypto.encrypt_file(args.input, output_file, password, public_key_pem)
        
        print(f"✅ تم التشفير بنجاح!")
        print(f"📄 الملف المشفر: {output_file}")
        
        # عرض معلومات إضافية
        file_size = os.path.getsize(output_file)
        print(f"📊 حجم الملف المشفر: {file_size:,} بايت")
        
        if public_key_pem:
            print("🔐 تم استخدام التشفير الهجين (AES + RSA)")
        else:
            print("🔐 تم استخدام التشفير المتماثل (AES)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشفير: {e}")
        return False


def decrypt_command(args):
    """تنفيذ أمر فك التشفير"""
    crypto = QuantumResistantCrypto()
    
    try:
        # التحقق من وجود الملف
        if not os.path.exists(args.input):
            print(f"❌ الملف المشفر غير موجود: {args.input}")
            return False
        
        # قراءة الملف المشفر
        with open(args.input, 'r', encoding='utf-8') as f:
            encrypted_package = json.load(f)
        
        # تحديد نوع فك التشفير
        uses_rsa = encrypted_package.get('uses_rsa', False)
        
        if uses_rsa:
            # فك التشفير الهجين
            if not args.private_key:
                print("❌ مطلوب ملف المفتاح الخاص لفك التشفير الهجين")
                return False
            
            if not os.path.exists(args.private_key):
                print(f"❌ ملف المفتاح الخاص غير موجود: {args.private_key}")
                return False
            
            with open(args.private_key, 'rb') as f:
                private_key_pem = f.read()
            
            print("🔓 جاري فك التشفير الهجين...")
            decrypted_data = crypto.decrypt_data(encrypted_package, private_key_pem=private_key_pem)
            
        else:
            # فك التشفير المتماثل
            if args.password:
                password = args.password
            else:
                password = getpass.getpass("🔑 أدخل كلمة المرور: ")
            
            print("🔓 جاري فك التشفير...")
            decrypted_data = crypto.decrypt_data(encrypted_package, password)
        
        # تحديد ملف الإخراج
        output_file = args.output or args.input.replace('.encrypted', '.decrypted')
        
        # كتابة البيانات المفكوكة
        with open(output_file, 'wb') as f:
            f.write(decrypted_data)
        
        print(f"✅ تم فك التشفير بنجاح!")
        print(f"📄 الملف المفكوك: {output_file}")
        print(f"📊 حجم البيانات: {len(decrypted_data):,} بايت")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فك التشفير: {e}")
        return False


def generate_keys_command(args):
    """تنفيذ أمر توليد المفاتيح"""
    crypto = QuantumResistantCrypto()
    
    try:
        print("🔑 جاري توليد زوج مفاتيح RSA-4096...")
        
        private_key, public_key = crypto.generate_rsa_keypair()
        
        # تحديد أسماء الملفات
        private_key_file = args.private_key or "private_key.pem"
        public_key_file = args.public_key or "public_key.pem"
        
        # كتابة المفاتيح
        with open(private_key_file, 'wb') as f:
            f.write(private_key)
        
        with open(public_key_file, 'wb') as f:
            f.write(public_key)
        
        print(f"✅ تم توليد المفاتيح بنجاح!")
        print(f"🔒 المفتاح الخاص: {private_key_file}")
        print(f"🔓 المفتاح العام: {public_key_file}")
        print("\n⚠️  تنبيه أمني:")
        print("   - احتفظ بالمفتاح الخاص في مكان آمن")
        print("   - لا تشارك المفتاح الخاص مع أي شخص")
        print("   - يمكن مشاركة المفتاح العام بأمان")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في توليد المفاتيح: {e}")
        return False


def generate_password_command(args):
    """تنفيذ أمر توليد كلمة مرور"""
    crypto = QuantumResistantCrypto()
    
    try:
        length = args.length or 32
        password = crypto.generate_secure_password(length)
        
        print(f"🔑 كلمة مرور آمنة ({length} حرف):")
        print(f"   {password}")
        print("\n💡 نصائح:")
        print("   - احفظ كلمة المرور في مكان آمن")
        print("   - لا تشارك كلمة المرور مع أي شخص")
        print("   - استخدم مدير كلمات مرور موثوق")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في توليد كلمة المرور: {e}")
        return False


def benchmark_command(args):
    """تنفيذ أمر قياس الأداء"""
    crypto = QuantumResistantCrypto()
    
    try:
        size = args.size or 1024*1024  # 1MB افتراضي
        
        print(f"📊 جاري قياس أداء التشفير لحجم {size/1024/1024:.1f} MB...")
        
        results = crypto.benchmark_encryption(size)
        
        print("\n📈 نتائج قياس الأداء:")
        print(f"   📏 حجم البيانات: {results['data_size_mb']:.2f} MB")
        print(f"   ⏱️  وقت التشفير: {results['encryption_time_seconds']:.4f} ثانية")
        print(f"   ⏱️  وقت فك التشفير: {results['decryption_time_seconds']:.4f} ثانية")
        print(f"   🚀 سرعة التشفير: {results['encryption_speed_mbps']:.2f} MB/s")
        print(f"   🚀 سرعة فك التشفير: {results['decryption_speed_mbps']:.2f} MB/s")
        print(f"   ⏰ الوقت الإجمالي: {results['total_time_seconds']:.4f} ثانية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قياس الأداء: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description="خوارزمية التشفير المتقدمة المقاومة للحوسبة الكمية",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='الأوامر المتاحة')
    
    # أمر التشفير
    encrypt_parser = subparsers.add_parser('encrypt', help='تشفير ملف')
    encrypt_parser.add_argument('input', help='الملف المراد تشفيره')
    encrypt_parser.add_argument('-o', '--output', help='ملف الإخراج')
    encrypt_parser.add_argument('-p', '--password', help='كلمة المرور')
    encrypt_parser.add_argument('-k', '--public-key', help='ملف المفتاح العام للتشفير الهجين')
    encrypt_parser.add_argument('-d', '--data-mode', action='store_true', 
                               help='وضع البيانات المباشر')
    
    # أمر فك التشفير
    decrypt_parser = subparsers.add_parser('decrypt', help='فك تشفير ملف')
    decrypt_parser.add_argument('input', help='الملف المشفر')
    decrypt_parser.add_argument('-o', '--output', help='ملف الإخراج')
    decrypt_parser.add_argument('-p', '--password', help='كلمة المرور')
    decrypt_parser.add_argument('-k', '--private-key', help='ملف المفتاح الخاص للتشفير الهجين')
    
    # أمر توليد المفاتيح
    keys_parser = subparsers.add_parser('generate-keys', help='توليد زوج مفاتيح RSA')
    keys_parser.add_argument('-priv', '--private-key', help='اسم ملف المفتاح الخاص')
    keys_parser.add_argument('-pub', '--public-key', help='اسم ملف المفتاح العام')
    
    # أمر توليد كلمة مرور
    password_parser = subparsers.add_parser('generate-password', help='توليد كلمة مرور آمنة')
    password_parser.add_argument('-l', '--length', type=int, help='طول كلمة المرور')
    
    # أمر قياس الأداء
    benchmark_parser = subparsers.add_parser('benchmark', help='قياس أداء التشفير')
    benchmark_parser.add_argument('-s', '--size', type=int, help='حجم البيانات بالبايت')
    
    args = parser.parse_args()
    
    # طباعة الشعار
    print_banner()
    
    # تنفيذ الأمر المطلوب
    if args.command == 'encrypt':
        success = encrypt_command(args)
    elif args.command == 'decrypt':
        success = decrypt_command(args)
    elif args.command == 'generate-keys':
        success = generate_keys_command(args)
    elif args.command == 'generate-password':
        success = generate_password_command(args)
    elif args.command == 'benchmark':
        success = benchmark_command(args)
    else:
        parser.print_help()
        success = False
    
    # إنهاء البرنامج
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
