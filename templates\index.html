<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 خوارزمية التشفير المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            padding-top: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            color: #667eea;
            font-weight: bold;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .progress {
            border-radius: 10px;
            height: 8px;
        }
        .progress-bar {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        .result-box {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            border-left: 4px solid #667eea;
        }
        .stats-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header">
                        <h1 class="mb-0">
                            <i class="fas fa-shield-alt"></i>
                            خوارزمية التشفير المتقدمة المقاومة للحوسبة الكمية
                        </h1>
                        <p class="mb-0 mt-2">تشفير متعدد الطبقات مع AES-256-GCM + RSA-4096</p>
                    </div>
                    <div class="card-body">
                        <!-- التبويبات -->
                        <ul class="nav nav-tabs mb-4" id="cryptoTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="universal-tab" data-bs-toggle="tab" data-bs-target="#universal-decrypt" type="button">
                                    <i class="fas fa-universal-access"></i> فك التشفير الشامل
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="text-tab" data-bs-toggle="tab" data-bs-target="#text-crypto" type="button">
                                    <i class="fas fa-font"></i> تشفير النصوص
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="file-tab" data-bs-toggle="tab" data-bs-target="#file-crypto" type="button">
                                    <i class="fas fa-file"></i> تشفير الملفات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="tools-tab" data-bs-toggle="tab" data-bs-target="#tools" type="button">
                                    <i class="fas fa-tools"></i> الأدوات
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="cryptoTabsContent">
                            <!-- تبويب فك التشفير الشامل -->
                            <div class="tab-pane fade" id="universal-decrypt" role="tabpanel">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card border-0 shadow-sm mb-4">
                                            <div class="card-header bg-primary text-white">
                                                <h5 class="mb-0"><i class="fas fa-unlock-alt me-2"></i>فك تشفير الملفات</h5>
                                            </div>
                                            <div class="card-body">
                                                <div id="universal-decrypt-container">
                                                    <div class="text-center p-4" id="upload-area">
                                                        <i class="fas fa-file-upload fa-4x text-muted mb-3"></i>
                                                        <h5>اسحب وأفلت الملف هنا</h5>
                                                        <p class="text-muted mb-3">أو انقر لاختيار ملف</p>
                                                        <input type="file" id="universal-file-input" class="d-none" accept=".enc,.aes,.des,.3des,.rsa,.gpg,.pgp,.7z,.zip,.rar,.gz,.bz2,.xz">
                                                        <button class="btn btn-primary" onclick="document.getElementById('universal-file-input').click()">
                                                            <i class="fas fa-folder-open me-2"></i>اختر ملفًا
                                                        </button>
                                                    </div>
                                                    <div id="file-info" class="d-none">
                                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                                            <div>
                                                                <h6 class="mb-1" id="file-name"></h6>
                                                                <small class="text-muted" id="file-size"></small>
                                                            </div>
                                                            <button type="button" class="btn btn-sm btn-outline-danger" id="remove-file">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                        <div class="progress mb-3" style="height: 5px;">
                                                            <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                                        </div>
                                                        <div id="archive-password-section" class="mb-3 d-none">
                                                            <label for="archive-password" class="form-label">كلمة مرور الأرشيف (اختياري)</label>
                                                            <div class="input-group">
                                                                <input type="password" class="form-control" id="archive-password" placeholder="أدخل كلمة المرور إذا كان الأرشيف محمي">
                                                                <button class="btn btn-outline-secondary" type="button" id="toggle-password" data-show="false">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label for="decryption-password" class="form-label">كلمة المرور (إذا كانت مطلوبة)</label>
                                                                <div class="input-group">
                                                                    <input type="password" class="form-control" id="decryption-password" placeholder="أدخل كلمة المرور">
                                                                    <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                                                        <i class="fas fa-eye"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <button class="btn btn-primary w-100" id="start-decryption">
                                                                <i class="fas fa-unlock me-2"></i>بدء فك التشفير
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- تبويب تشفير النصوص -->
                            <div class="tab-pane fade show active" id="text-crypto" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-lock"></i> التشفير</h5>
                                        <div class="mb-3">
                                            <label class="form-label">النص المراد تشفيره:</label>
                                            <textarea class="form-control" id="plainText" rows="5" placeholder="أدخل النص هنا..."></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">كلمة المرور:</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="encryptPassword" placeholder="كلمة مرور قوية">
                                                <button class="btn btn-outline-secondary" type="button" id="generatePassword">
                                                    <i class="fas fa-random"></i> توليد
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="useRSA">
                                                <label class="form-check-label" for="useRSA">
                                                    استخدام التشفير الهجين (RSA + AES)
                                                </label>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary w-100" id="encryptBtn">
                                            <i class="fas fa-lock"></i> تشفير
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-unlock"></i> فك التشفير</h5>
                                        <div class="mb-3">
                                            <label class="form-label">البيانات المشفرة (JSON):</label>
                                            <textarea class="form-control" id="encryptedData" rows="5" placeholder="الصق البيانات المشفرة هنا..."></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">كلمة المرور:</label>
                                            <input type="password" class="form-control" id="decryptPassword" placeholder="كلمة المرور">
                                        </div>
                                        <button class="btn btn-primary w-100" id="decryptBtn">
                                            <i class="fas fa-unlock"></i> فك التشفير
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- منطقة النتائج -->
                                <div id="textResults" class="mt-4"></div>
                            </div>

                            <!-- تبويب تشفير الملفات -->
                            <div class="tab-pane fade" id="file-crypto" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-file-lock"></i> تشفير ملف</h5>
                                        <div class="mb-3">
                                            <label class="form-label">اختر الملف:</label>
                                            <input type="file" class="form-control" id="fileToEncrypt">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">كلمة المرور:</label>
                                            <input type="password" class="form-control" id="fileEncryptPassword" placeholder="كلمة مرور قوية">
                                        </div>
                                        <button class="btn btn-primary w-100" id="encryptFileBtn">
                                            <i class="fas fa-file-lock"></i> تشفير وتحميل
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-file-unlock"></i> فك تشفير ملف</h5>
                                        <div class="mb-3">
                                            <label class="form-label">اختر الملف المشفر:</label>
                                            <input type="file" class="form-control" id="fileToDecrypt" accept=".encrypted">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">كلمة المرور:</label>
                                            <input type="password" class="form-control" id="fileDecryptPassword" placeholder="كلمة المرور">
                                        </div>
                                        <button class="btn btn-primary w-100" id="decryptFileBtn">
                                            <i class="fas fa-file-unlock"></i> فك التشفير وتحميل
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب الأدوات -->
                            <div class="tab-pane fade" id="tools" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-key"></i> توليد كلمة مرور آمنة</h5>
                                        <div class="mb-3">
                                            <label class="form-label">طول كلمة المرور:</label>
                                            <select class="form-select" id="passwordLength">
                                                <option value="16">16 حرف</option>
                                                <option value="32" selected>32 حرف</option>
                                                <option value="64">64 حرف</option>
                                                <option value="128">128 حرف</option>
                                            </select>
                                        </div>
                                        <button class="btn btn-primary w-100" id="generateSecurePassword">
                                            <i class="fas fa-key"></i> توليد كلمة مرور
                                        </button>
                                        <div id="generatedPassword" class="result-box mt-3" style="display: none;">
                                            <strong>كلمة المرور المولدة:</strong>
                                            <div class="mt-2">
                                                <input type="text" class="form-control" id="passwordResult" readonly>
                                                <button class="btn btn-sm btn-outline-primary mt-2" onclick="copyToClipboard('passwordResult')">
                                                    <i class="fas fa-copy"></i> نسخ
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-tachometer-alt"></i> قياس الأداء</h5>
                                        <div class="mb-3">
                                            <label class="form-label">حجم البيانات للاختبار:</label>
                                            <select class="form-select" id="benchmarkSize">
                                                <option value="1024">1 KB</option>
                                                <option value="10240">10 KB</option>
                                                <option value="102400">100 KB</option>
                                                <option value="1048576" selected>1 MB</option>
                                            </select>
                                        </div>
                                        <button class="btn btn-primary w-100" id="runBenchmark">
                                            <i class="fas fa-tachometer-alt"></i> قياس الأداء
                                        </button>
                                        <div id="benchmarkResults" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // دالة لنسخ النص إلى الحافظة
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            document.execCommand('copy');
            showAlert('تم نسخ النص إلى الحافظة!', 'success');
        }

        // دالة لعرض التنبيهات
        function showAlert(message, type = 'info') {
            const alertDiv = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#textResults').html(alertDiv);
        }

        // توليد كلمة مرور سريعة
        $('#generatePassword').click(function() {
            $.get('/api/generate-password?length=32', function(data) {
                if (data.success) {
                    $('#encryptPassword').val(data.password);
                    showAlert('تم توليد كلمة مرور آمنة!', 'success');
                }
            });
        });

        // تشفير النص
        $('#encryptBtn').click(function() {
            const text = $('#plainText').val();
            const password = $('#encryptPassword').val();
            const useRSA = $('#useRSA').is(':checked');

            if (!text || !password) {
                showAlert('يرجى إدخال النص وكلمة المرور!', 'warning');
                return;
            }

            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري التشفير...');

            $.ajax({
                url: '/api/encrypt',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    text: text,
                    password: password,
                    use_rsa: useRSA
                }),
                success: function(data) {
                    if (data.success) {
                        const result = `
                            <div class="result-box">
                                <h6><i class="fas fa-check-circle text-success"></i> تم التشفير بنجاح!</h6>
                                <div class="stats-card">
                                    <div class="row text-center">
                                        <div class="col-3">
                                            <div><i class="fas fa-clock"></i></div>
                                            <div>${data.encryption_time}s</div>
                                            <small>وقت التشفير</small>
                                        </div>
                                        <div class="col-3">
                                            <div><i class="fas fa-database"></i></div>
                                            <div>${data.data_size} bytes</div>
                                            <small>حجم البيانات</small>
                                        </div>
                                        <div class="col-6">
                                            <div><i class="fas fa-shield-alt"></i></div>
                                            <div>${data.algorithm}</div>
                                            <small>الخوارزمية</small>
                                        </div>
                                    </div>
                                </div>
                                <label class="form-label mt-3">البيانات المشفرة:</label>
                                <textarea class="form-control" rows="8" readonly>${JSON.stringify(data.encrypted_data, null, 2)}</textarea>
                                <button class="btn btn-outline-primary mt-2" onclick="copyToClipboard('encryptedData')">
                                    <i class="fas fa-copy"></i> نسخ للفك التشفير
                                </button>
                            </div>
                        `;
                        $('#textResults').html(result);
                        $('#encryptedData').val(JSON.stringify(data.encrypted_data, null, 2));
                    } else {
                        showAlert(data.error, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم!', 'danger');
                },
                complete: function() {
                    $('#encryptBtn').prop('disabled', false).html('<i class="fas fa-lock"></i> تشفير');
                }
            });
        });

        // فك تشفير النص
        $('#decryptBtn').click(function() {
            const encryptedData = $('#encryptedData').val();
            const password = $('#decryptPassword').val();

            if (!encryptedData) {
                showAlert('يرجى إدخال البيانات المشفرة!', 'warning');
                return;
            }

            let parsedData;
            try {
                parsedData = JSON.parse(encryptedData);
            } catch (e) {
                showAlert('البيانات المشفرة غير صحيحة!', 'danger');
                return;
            }

            if (!parsedData.uses_rsa && !password) {
                showAlert('كلمة المرور مطلوبة!', 'warning');
                return;
            }

            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري فك التشفير...');

            $.ajax({
                url: '/api/decrypt',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    encrypted_data: parsedData,
                    password: password
                }),
                success: function(data) {
                    if (data.success) {
                        const result = `
                            <div class="result-box">
                                <h6><i class="fas fa-check-circle text-success"></i> تم فك التشفير بنجاح!</h6>
                                <div class="stats-card">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div><i class="fas fa-clock"></i></div>
                                            <div>${data.decryption_time}s</div>
                                            <small>وقت فك التشفير</small>
                                        </div>
                                        <div class="col-6">
                                            <div><i class="fas fa-database"></i></div>
                                            <div>${data.data_size} bytes</div>
                                            <small>حجم البيانات</small>
                                        </div>
                                    </div>
                                </div>
                                <label class="form-label mt-3">النص المفكوك:</label>
                                <textarea class="form-control" rows="5" readonly>${data.decrypted_text}</textarea>
                            </div>
                        `;
                        $('#textResults').html(result);
                    } else {
                        showAlert(data.error, 'danger');
                    }
                },
                error: function() {
                    showAlert('خطأ في الاتصال بالخادم!', 'danger');
                },
                complete: function() {
                    $('#decryptBtn').prop('disabled', false).html('<i class="fas fa-unlock"></i> فك التشفير');
                }
            });
        });

        // توليد كلمة مرور آمنة
        $('#generateSecurePassword').click(function() {
            const length = $('#passwordLength').val();
            
            $.get(`/api/generate-password?length=${length}`, function(data) {
                if (data.success) {
                    $('#passwordResult').val(data.password);
                    $('#generatedPassword').show();
                }
            });
        });

        // قياس الأداء
        $('#runBenchmark').click(function() {
            const size = $('#benchmarkSize').val();
            
            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري القياس...');
            
            $.get(`/api/benchmark?size=${size}`, function(data) {
                if (data.success) {
                    const results = data.benchmark_results;
                    const resultHtml = `
                        <div class="stats-card">
                            <h6><i class="fas fa-chart-line"></i> نتائج قياس الأداء</h6>
                            <div class="row text-center mt-3">
                                <div class="col-6">
                                    <div><strong>${results.encryption_time_seconds.toFixed(4)}s</strong></div>
                                    <small>وقت التشفير</small>
                                </div>
                                <div class="col-6">
                                    <div><strong>${results.decryption_time_seconds.toFixed(4)}s</strong></div>
                                    <small>وقت فك التشفير</small>
                                </div>
                                <div class="col-6 mt-2">
                                    <div><strong>${results.encryption_speed_mbps.toFixed(2)} MB/s</strong></div>
                                    <small>سرعة التشفير</small>
                                </div>
                                <div class="col-6 mt-2">
                                    <div><strong>${results.decryption_speed_mbps.toFixed(2)} MB/s</strong></div>
                                    <small>سرعة فك التشفير</small>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#benchmarkResults').html(resultHtml);
                }
            }).always(function() {
                $('#runBenchmark').prop('disabled', false).html('<i class="fas fa-tachometer-alt"></i> قياس الأداء');
            });
        });

        // تشفير الملفات
        $('#encryptFileBtn').click(function() {
            const fileInput = document.getElementById('fileToEncrypt');
            const password = $('#fileEncryptPassword').val();
            
            if (!fileInput.files[0] || !password) {
                alert('يرجى اختيار ملف وإدخال كلمة مرور!');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('password', password);
            
            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري التشفير...');
            
            $.ajax({
                url: '/api/encrypt-file',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    const filename = xhr.getResponseHeader('Content-Disposition').split('filename=')[1].replace(/"/g, '');
                    const url = window.URL.createObjectURL(data);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    alert('تم تشفير الملف وتحميله بنجاح!');
                },
                error: function() {
                    alert('خطأ في تشفير الملف!');
                },
                complete: function() {
                    $('#encryptFileBtn').prop('disabled', false).html('<i class="fas fa-file-lock"></i> تشفير وتحميل');
                }
            });
        });

        // فك تشفير الملفات
        $('#decryptFileBtn').click(function() {
            const fileInput = document.getElementById('fileToDecrypt');
            const password = $('#fileDecryptPassword').val();
            
            if (!fileInput.files[0] || !password) {
                alert('يرجى اختيار ملف مشفر وإدخال كلمة مرور!');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('password', password);
            
            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري فك التشفير...');
            
            $.ajax({
                url: '/api/decrypt-file',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    const filename = xhr.getResponseHeader('Content-Disposition').split('filename=')[1].replace(/"/g, '');
                    const url = window.URL.createObjectURL(data);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    alert('تم فك تشفير الملف وتحميله بنجاح!');
                },
                error: function() {
                    alert('خطأ في فك تشفير الملف!');
                },
                complete: function() {
                    $('#decryptFileBtn').prop('disabled', false).html('<i class="fas fa-file-unlock"></i> فك التشفير وتحميل');
                }
            });
        });
        // Universal Decryption Handler
        $(document).ready(function() {
            const uploadArea = $('#upload-area');
            const fileInput = $('#file-input');
            const fileInfo = $('#file-info');
            const fileName = $('#file-name');
            const fileSize = $('#file-size');
            const removeFileBtn = $('#remove-file');
            const startDecryptionBtn = $('#start-decryption');
            const progressBar = $('#progress-bar');
            const analysisResults = $('#analysis-results');
            const archivePasswordSection = $('#archive-password-section');
            const archivePassword = $('#archive-password');
            const togglePasswordBtn = $('#toggle-password');
            const archiveContents = $('#archive-contents');
            const archiveFilesList = $('#archive-files-list');
            let currentSessionId = null;
            
            // Toggle password visibility
            togglePasswordBtn.on('click', function() {
                const type = archivePassword.attr('type') === 'password' ? 'text' : 'password';
                archivePassword.attr('type', type);
                togglePasswordBtn.find('i').toggleClass('fa-eye fa-eye-slash');
            });
            
            // Enable drag and drop
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.on(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // Highlight drop area when item is dragged over it
            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.on(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.on(eventName, unhighlight, false);
            });
            
            function highlight() {
                uploadArea.addClass('border border-primary');
            }
            
            function unhighlight() {
                uploadArea.removeClass('border border-primary');
            }
            
            // Handle dropped files
            uploadArea.on('drop', handleDrop, false);
            
            function handleDrop(e) {
                const dt = e.originalEvent.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }
            
            // Handle file selection
            fileInput.on('change', function() {
                handleFiles(this.files);
            });
            
            function handleFiles(files) {
                if (files.length > 0) {
                    const file = files[0];
                    showFileInfo(file);
                    analyzeFile(file);
                }
            }
            
            
            function showFileInfo(file) {
                fileName.text(file.name);
                fileSize.text(formatFileSize(file.size));
                uploadArea.addClass('d-none');
                fileInfo.removeClass('d-none');
            }
            
            // Format file size
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 بايت';
                const k = 1024;
                const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            // Remove file
            removeFileBtn.on('click', function() {
                fileInput.val('');
                uploadArea.removeClass('d-none');
                fileInfo.addClass('d-none');
                analysisResults.empty();
                decryptionOptions.addClass('d-none');
            });
            
            // Initialize password toggle
            $('.password-toggle').on('click', function() {
                const input = $(this).siblings('input');
                const type = input.attr('type') === 'password' ? 'text' : 'password';
                input.attr('type', type);
                $(this).find('i').toggleClass('fa-eye fa-eye-slash');
            });
            
            // Analyze file
            function analyzeFile(file) {
                const formData = new FormData();
                formData.append('file', file);
                
                showLoading('جاري تحليل الملف', 'الرجاء الانتظار...');
                
                $.ajax({
                    url: '/analyze',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        Swal.close();
                        if (response.status === 'success') {
                            displayAnalysisResults(response.analysis);
                        } else {
                            showError(response.error || 'فشل تحليل الملف');
                        }
                    },
                    error: function(xhr) {
                        Swal.close();
                        let errorMsg = 'حدث خطأ أثناء تحليل الملف';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.error || errorMsg;
                        } catch (e) {}
                        showError(errorMsg);
                    },
                    complete: function() {
                        startDecryptionBtn.prop('disabled', false);
                    }
                });
            }
            
            // Display analysis results
            function displayAnalysisResults(analysis) {
                let html = '';
                
                if (analysis.is_encrypted) {
                    html += `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-lock me-2"></i>تم اكتشاف تشفير</h6>
                            <p>يبدو أن الملف مشفر. ${analysis.possible_algorithms && analysis.possible_algorithms.length > 0 ? 'الخوارزميات المحتملة: ' + analysis.possible_algorithms.join('، ') : ''}</p>
                        </div>
                    `;
                    
                    // Show decryption options
                    decryptionOptions.removeClass('d-none');
                } else {
                    html += `
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>لم يتم اكتشاف تشفير</h6>
                            <p>لا يبدو أن الملف مشفر أو أن التشفير غير معروف.</p>
                            <p>إذا كنت تعتقد أن الملف مشفر، يمكنك تجربة فك التشفير يدويًا.</p>
                        </div>
                    `;
                    
                    // Still show decryption options but with a warning
                    decryptionOptions.removeClass('d-none');
                }
                
                // Add file info
                html += `
                    <div class="card mb-3">
                        <div class="card-body p-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <small class="text-muted">نوع الملف:</small>
                                    <div>${analysis.file_type || 'غير معروف'}</div>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">حجم الملف:</small>
                                    <div>${formatFileSize(analysis.size)}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                analysisResults.html(html);
                
                // Show password section for password-protected archives
                if (analysis.password_protected) {
                    archivePasswordSection.removeClass('d-none');
                    archivePassword.attr('placeholder', 'هذا الأرشيف محمي بكلمة مرور');
                } else {
                    archivePasswordSection.addClass('d-none');
                }
                    
                    // Display archive contents if available
                    if (analysis.archive_contents && analysis.archive_contents.length > 0) {
                        archiveContents.removeClass('d-none');
                        archiveFilesList.empty();
                        
                        analysis.archive_contents.forEach(file => {
                            const fileSize = file.size ? formatFileSize(file.size) : 'غير معروف';
                            const fileDate = file.modified ? 
                                new Date(file.modified * 1000).toLocaleString() : 'غير معروف';
                            const encryptedBadge = file.encrypted ? 
                                '<span class="badge bg-warning text-dark">مشفر</span> ' : '';
                            
                            const fileItem = $('<div class="list-group-item d-flex justify-content-between align-items-center">')
                                .append($('<div>').html(`
                                    <i class="fas fa-file me-2"></i>${file.filename}
                                    ${encryptedBadge}
                                `))
                                .append($('<div class="text-muted small">').text(`${fileSize} • ${fileDate}`));
                                
                            archiveFilesList.append(fileItem);
                        });
                    }
                } else {
                    archivePasswordSection.addClass('d-none');
                    archiveContents.addClass('d-none');
                }
                    
                    // Show decryption options
                    decryptionOptions.removeClass('d-none');
                } else {
                    html += `
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>لم يتم اكتشاف تشفير</h6>
                            <p>لا يبدو أن الملف مشفر أو أن التشفير غير معروف.</p>
                            <p>إذا كنت تعتقد أن الملف مشفر، يمكنك تجربة فك التشفير يدويًا.</p>
                        </div>
                    `;
                    
                    // Still show decryption options but with a warning
                    decryptionOptions.removeClass('d-none');
                }
                
                // Add file info
                html += `
                    <div class="card mb-3">
                        <div class="card-body p-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <small class="text-muted">نوع الملف:</small>
                                    <div>${analysis.file_type || 'غير معروف'}</div>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">حجم الملف:</small>
                                    <div>${formatFileSize(analysis.size)}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                analysisResults.html(html);
            }
            
            // Start decryption or extraction
            startDecryptionBtn.on('click', function() {
                const file = fileInput[0].files[0];
                if (!file) {
                    showError('الرجاء اختيار ملف أولاً');
                    return;
                }
                
                const password = $('#archive-password').val() || null;
                showLoading('جاري معالجة الملف', 'قد تستغرق العملية بعض الوقت...');
                
                startDecryptionBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري المعالجة...');
                
                // If we already have a session ID, use it for extraction
                if (currentSessionId) {
                    extractArchive(currentSessionId, password);
                    return;
                }
                
                // Otherwise, upload and analyze the file first
                const formData = new FormData();
                formData.append('file', file);
                
                $.ajax({
                    url: '/upload',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhr: function() {
                        const xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener('progress', function(e) {
                            if (e.lengthComputable) {
                                const percentComplete = (e.loaded / e.total) * 100;
                                progressBar.css('width', percentComplete + '%').attr('aria-valuenow', percentComplete);
                            }
                        }, false);
                        return xhr;
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            Swal.close();
                            currentSessionId = response.session_id;
                            displayAnalysisResults(response.analysis);
                            
                            // If it's an archive, handle extraction
                            if (response.is_archive) {
                                extractArchive(currentSessionId, password);
                            } else {
                                // Handle regular decryption
                                startDecryptionBtn.html('<i class="fas fa-unlock me-2"></i>بدء فك التشفير');
                                showSuccess('نجاح', 'تم تحليل الملف بنجاح');
                            }
                        } else {
                            showError(response.error || 'حدث خطأ أثناء تحليل الملف');
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = 'حدث خطأ أثناء معالجة الملف';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.error || errorMsg;
                        } catch (e) {}
                        showError(errorMsg);
                    },
                    complete: function() {
                        // Don't re-enable the button if we're in the middle of extraction
                        if (!currentSessionId) {
                            startDecryptionBtn.prop('disabled', false).html('<i class="fas fa-unlock me-2"></i>بدء فك التشفير');
                        }
                    }
                });
            });
            
            // Function to handle archive extraction
            function extractArchive(sessionId, password = null) {
                startDecryptionBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري استخراج الملفات...');
                
                $.ajax({
                    url: '/extract',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        session_id: sessionId,
                        password: password
                    }),
                    success: function(response) {
                        if (response.status === 'success') {
                            showSuccess('نجاح', response.message || 'تم استخراج الملفات بنجاح');
                            
                            // Update the archive contents display
                            if (response.extracted_files && response.extracted_files.length > 0) {
                                archiveFilesList.empty();
                                response.extracted_files.forEach(file => {
                                    const fileSize = file.size ? formatFileSize(file.size) : 'غير معروف';
                                    const fileDate = file.modified ? 
                                        new Date(file.modified * 1000).toLocaleString() : 'غير معروف';
                                    const downloadUrl = `/download/${sessionId}/${encodeURIComponent(file.filename)}`;
                                    
                                    const fileItem = $('<div class="list-group-item d-flex justify-content-between align-items-center">')
                                        .append($('<div>').html(`
                                            <i class="fas fa-file me-2"></i>${file.filename}
                                        `))
                                        .append($('<div>').html(`
                                            <span class="text-muted small me-3">${fileSize} • ${fileDate}</span>
                                            <a href="${downloadUrl}" class="btn btn-sm btn-outline-primary" download>
                                                <i class="fas fa-download"></i>
                                            </a>
                                        `));
                                        
                                    archiveFilesList.append(fileItem);
                                });
                                archiveContents.removeClass('d-none');
                            }
                            
                            // If still password protected, show error
                            if (response.password_protected && !response.success) {
                                showError('كلمة المرور غير صحيحة أو غير كافية لاستخراج الملفات');
                            }
                        } else {
                            showError(response.error || 'حدث خطأ أثناء استخراج الملفات');
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = 'حدث خطأ أثناء استخراج الملفات';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.error || errorMsg;
                        } catch (e) {}
                        showError(errorMsg);
                    },
                    complete: function() {
                        startDecryptionBtn.prop('disabled', false).html('<i class="fas fa-unlock me-2"></i>استخراج الملفات');
                    }
                });
            }
            
            // Toggle password visibility
            $('#toggle-password').on('click', function() {
                const passwordInput = $('#archive-password');
                const isPasswordVisible = $(this).data('show');
                
                if (isPasswordVisible) {
                    passwordInput.attr('type', 'password');
                    $(this).html('<i class="fas fa-eye"></i>');
                } else {
                    passwordInput.attr('type', 'text');
                    $(this).html('<i class="fas fa-eye-slash"></i>');
                }
                
                $(this).data('show', !isPasswordVisible);
            });
            
            // Helper functions
            function showError(message) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: message,
                    confirmButtonText: 'حسنًا',
                    confirmButtonColor: '#667eea'
                });
            }
            
            function showSuccess(title, message) {
                Swal.fire({
                    icon: 'success',
                    title: title,
                    text: message,
                    confirmButtonText: 'حسنًا',
                    confirmButtonColor: '#667eea'
                });
            }
            
            function showLoading(title = 'جاري المعالجة', text = 'الرجاء الانتظار...') {
                Swal.fire({
                    title: title,
                    text: text,
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        });
    </script>
</body>
</html>
