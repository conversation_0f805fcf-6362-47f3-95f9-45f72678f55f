#!/usr/bin/env python3
"""
اختبارات شاملة لخوارزمية التشفير المتقدمة
Comprehensive tests for Advanced Quantum-Resistant Encryption Algorithm
"""

import unittest
import os
import json
import secrets
from advanced_crypto import QuantumResistantCrypto


class TestQuantumResistantCrypto(unittest.TestCase):
    """فئة اختبار خوارزمية التشفير"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.crypto = QuantumResistantCrypto()
        self.test_password = "test_password_123!@#"
        self.test_data = "هذا نص تجريبي للاختبار 🔐".encode('utf-8')
    
    def test_basic_encryption_decryption(self):
        """اختبار التشفير وفك التشفير الأساسي"""
        # التشفير
        encrypted = self.crypto.encrypt_data(self.test_data, self.test_password)
        
        # التحقق من وجود العناصر المطلوبة
        required_keys = ['version', 'timestamp', 'salt', 'iv', 'tag', 
                        'ciphertext', 'integrity_hash', 'algorithm']
        for key in required_keys:
            self.assertIn(key, encrypted)
        
        # فك التشفير
        decrypted = self.crypto.decrypt_data(encrypted, self.test_password)
        
        # التحقق من صحة النتيجة
        self.assertEqual(self.test_data, decrypted)
    
    def test_rsa_hybrid_encryption(self):
        """اختبار التشفير الهجين مع RSA"""
        # توليد مفاتيح RSA
        private_key, public_key = self.crypto.generate_rsa_keypair()
        
        # التشفير الهجين
        encrypted = self.crypto.encrypt_data(self.test_data, public_key_pem=public_key)
        
        # التحقق من استخدام RSA
        self.assertTrue(encrypted['uses_rsa'])
        self.assertIn('rsa_encrypted_key', encrypted)
        
        # فك التشفير
        decrypted = self.crypto.decrypt_data(encrypted, private_key_pem=private_key)
        
        # التحقق من صحة النتيجة
        self.assertEqual(self.test_data, decrypted)
    
    def test_file_encryption_decryption(self):
        """اختبار تشفير وفك تشفير الملفات"""
        # إنشاء ملف تجريبي
        test_file = "test_file.txt"
        test_content = "محتوى ملف تجريبي للاختبار\nسطر ثاني\nسطر ثالث 🔐"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        try:
            # تشفير الملف
            encrypted_file = self.crypto.encrypt_file(test_file, password=self.test_password)
            self.assertTrue(os.path.exists(encrypted_file))
            
            # فك تشفير الملف
            decrypted_file = self.crypto.decrypt_file(encrypted_file, password=self.test_password)
            self.assertTrue(os.path.exists(decrypted_file))
            
            # التحقق من المحتوى
            with open(decrypted_file, 'r', encoding='utf-8') as f:
                decrypted_content = f.read()
            
            self.assertEqual(test_content, decrypted_content)
            
        finally:
            # تنظيف الملفات
            for file in [test_file, encrypted_file, decrypted_file]:
                if os.path.exists(file):
                    os.remove(file)
    
    def test_wrong_password(self):
        """اختبار رفض كلمة مرور خاطئة"""
        encrypted = self.crypto.encrypt_data(self.test_data, self.test_password)
        
        with self.assertRaises(ValueError):
            self.crypto.decrypt_data(encrypted, "wrong_password")
    
    def test_tamper_detection(self):
        """اختبار اكتشاف التلاعب بالبيانات"""
        encrypted = self.crypto.encrypt_data(self.test_data, self.test_password)
        
        # تعديل البيانات المشفرة
        tampered = encrypted.copy()
        tampered['ciphertext'] = tampered['ciphertext'][:-1] + 'X'
        
        with self.assertRaises(ValueError):
            self.crypto.decrypt_data(tampered, self.test_password)
    
    def test_integrity_hash_tamper(self):
        """اختبار اكتشاف تعديل hash السلامة"""
        encrypted = self.crypto.encrypt_data(self.test_data, self.test_password)
        
        # تعديل hash السلامة
        tampered = encrypted.copy()
        tampered['integrity_hash'] = tampered['integrity_hash'][:-1] + 'X'
        
        with self.assertRaises(ValueError):
            self.crypto.decrypt_data(tampered, self.test_password)
    
    def test_different_data_sizes(self):
        """اختبار أحجام مختلفة من البيانات"""
        test_sizes = [1, 100, 1000, 10000, 100000]
        
        for size in test_sizes:
            with self.subTest(size=size):
                test_data = secrets.token_bytes(size)
                
                encrypted = self.crypto.encrypt_data(test_data, self.test_password)
                decrypted = self.crypto.decrypt_data(encrypted, self.test_password)
                
                self.assertEqual(test_data, decrypted)
    
    def test_unicode_data(self):
        """اختبار البيانات متعددة اللغات"""
        unicode_texts = [
            "مرحبا بالعالم! 🌍",
            "Hello World! 🌎",
            "こんにちは世界！🌏",
            "Привет мир! 🌍",
            "¡Hola Mundo! 🌎"
        ]
        
        for text in unicode_texts:
            with self.subTest(text=text):
                data = text.encode('utf-8')
                
                encrypted = self.crypto.encrypt_data(data, self.test_password)
                decrypted = self.crypto.decrypt_data(encrypted, self.test_password)
                
                self.assertEqual(data, decrypted)
                self.assertEqual(text, decrypted.decode('utf-8'))
    
    def test_empty_data(self):
        """اختبار البيانات الفارغة"""
        empty_data = b""
        
        encrypted = self.crypto.encrypt_data(empty_data, self.test_password)
        decrypted = self.crypto.decrypt_data(encrypted, self.test_password)
        
        self.assertEqual(empty_data, decrypted)
    
    def test_secure_password_generation(self):
        """اختبار توليد كلمات مرور آمنة"""
        for length in [16, 32, 64, 128]:
            with self.subTest(length=length):
                password = self.crypto.generate_secure_password(length)
                
                self.assertEqual(len(password), length)
                self.assertTrue(any(c.isupper() for c in password))
                self.assertTrue(any(c.islower() for c in password))
                self.assertTrue(any(c.isdigit() for c in password))
    
    def test_rsa_key_generation(self):
        """اختبار توليد مفاتيح RSA"""
        private_key, public_key = self.crypto.generate_rsa_keypair()
        
        # التحقق من تنسيق المفاتيح
        self.assertTrue(private_key.startswith(b'-----BEGIN PRIVATE KEY-----'))
        self.assertTrue(private_key.endswith(b'-----END PRIVATE KEY-----\n'))
        self.assertTrue(public_key.startswith(b'-----BEGIN PUBLIC KEY-----'))
        self.assertTrue(public_key.endswith(b'-----END PUBLIC KEY-----\n'))
    
    def test_benchmark_functionality(self):
        """اختبار وظيفة قياس الأداء"""
        results = self.crypto.benchmark_encryption(1024)  # 1KB
        
        required_metrics = [
            'data_size_mb', 'encryption_time_seconds', 'decryption_time_seconds',
            'encryption_speed_mbps', 'decryption_speed_mbps', 'total_time_seconds'
        ]
        
        for metric in required_metrics:
            self.assertIn(metric, results)
            self.assertIsInstance(results[metric], (int, float))
            self.assertGreater(results[metric], 0)
    
    def test_version_compatibility(self):
        """اختبار توافق الإصدارات"""
        encrypted = self.crypto.encrypt_data(self.test_data, self.test_password)
        
        # التحقق من الإصدار
        self.assertEqual(encrypted['version'], '1.0')
        
        # محاولة فك تشفير إصدار غير مدعوم
        invalid_version = encrypted.copy()
        invalid_version['version'] = '2.0'
        
        with self.assertRaises(ValueError):
            self.crypto.decrypt_data(invalid_version, self.test_password)
    
    def test_algorithm_identifier(self):
        """اختبار معرف الخوارزمية"""
        encrypted = self.crypto.encrypt_data(self.test_data, self.test_password)
        
        expected_algorithm = "QuantumResistant-AES256-GCM-RSA4096"
        self.assertEqual(encrypted['algorithm'], expected_algorithm)
    
    def test_master_password_functionality(self):
        """اختبار وظيفة كلمة المرور الرئيسية"""
        master_password = "master_password_123"
        crypto_with_master = QuantumResistantCrypto(master_password)
        
        # التشفير بدون تحديد كلمة مرور (يجب استخدام الرئيسية)
        encrypted = crypto_with_master.encrypt_data(self.test_data)
        decrypted = crypto_with_master.decrypt_data(encrypted)
        
        self.assertEqual(self.test_data, decrypted)


class TestSecurityFeatures(unittest.TestCase):
    """اختبارات المميزات الأمنية"""
    
    def setUp(self):
        self.crypto = QuantumResistantCrypto()
        self.password = "secure_test_password"
        self.data = b"sensitive test data"
    
    def test_salt_uniqueness(self):
        """اختبار تفرد الملح في كل عملية تشفير"""
        encrypted1 = self.crypto.encrypt_data(self.data, self.password)
        encrypted2 = self.crypto.encrypt_data(self.data, self.password)
        
        # يجب أن يكون الملح مختلف في كل مرة
        self.assertNotEqual(encrypted1['salt'], encrypted2['salt'])
        
        # يجب أن يكون النص المشفر مختلف
        self.assertNotEqual(encrypted1['ciphertext'], encrypted2['ciphertext'])
    
    def test_iv_uniqueness(self):
        """اختبار تفرد المتجه الأولي"""
        encrypted1 = self.crypto.encrypt_data(self.data, self.password)
        encrypted2 = self.crypto.encrypt_data(self.data, self.password)
        
        # يجب أن يكون IV مختلف في كل مرة
        self.assertNotEqual(encrypted1['iv'], encrypted2['iv'])


if __name__ == '__main__':
    print("🧪 تشغيل الاختبارات الشاملة لخوارزمية التشفير")
    print("=" * 60)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
