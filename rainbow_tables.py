"""
Rainbow table implementation for password recovery
"""
import os
import json
import hashlib
import pickle
import logging
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger('RainbowTables')

@dataclass
class RainbowTableChain:
    """Represents a single chain in a rainbow table"""
    start: str
    end: str
    length: int

class RainbowTable:
    """Rainbow table implementation for password recovery"""
    
    def __init__(self, algorithm: str = 'md5', chain_length: int = 1000, 
                 table_size: int = 10000, charset: str = None):
        """
        Initialize a new rainbow table
        
        Args:
            algorithm: Hash algorithm to use (e.g., 'md5', 'sha1', 'sha256')
            chain_length: Length of each chain
            table_size: Number of chains in the table
            charset: Character set to use for password generation
        """
        self.algorithm = algorithm.lower()
        self.chain_length = chain_length
        self.table_size = table_size
        self.charset = charset or (
            'abcdefghijklmnopqrstuvwxyz'
            'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
            '0123456789!@#$%^&*()_+-=[]{}|;:,.<>?'
        )
        self.chains: List[RainbowTableChain] = []
        self.chain_map: Dict[str, RainbowTableChain] = {}
        self.loaded = False
    
    def generate(self, output_file: str = None) -> None:
        """Generate a new rainbow table"""
        logger.info(f"Generating rainbow table with {self.table_size} chains...")
        
        # Clear existing chains
        self.chains = []
        self.chain_map = {}
        
        # Generate chains in parallel
        import multiprocessing as mp
        from tqdm import tqdm
        
        # Generate start points
        start_points = set()
        while len(start_points) < self.table_size:
            start_points.add(self._random_string(8))
        start_points = list(start_points)[:self.table_size]
        
        # Process chains in parallel
        with mp.Pool() as pool:
            results = list(tqdm(
                pool.imap(self._generate_chain, start_points),
                total=len(start_points),
                desc="Generating chains"
            ))
        
        # Store valid chains
        for start, end, length in results:
            if start and end:
                chain = RainbowTableChain(start=start, end=end, length=length)
                self.chains.append(chain)
                self.chain_map[end] = chain
        
        logger.info(f"Generated {len(self.chains)} chains")
        
        # Save if output file is specified
        if output_file:
            self.save(output_file)
    
    def _generate_chain(self, start: str) -> Tuple[str, str, int]:
        """Generate a single chain"""
        current = start
        
        for i in range(self.chain_length):
            # Hash the current value
            h = self._hash(current)
            
            # Reduce to get next plaintext
            current = self._reduce(h, i)
            
            # Early termination if we hit a cycle
            if not current:
                return None, None, 0
        
        return start, current, self.chain_length
    
    def lookup(self, hash_value: str) -> Optional[str]:
        """
        Look up a hash in the rainbow table
        
        Args:
            hash_value: The hash to look up
            
        Returns:
            The plaintext password if found, None otherwise
        """
        if not self.chains:
            logger.warning("No chains loaded in rainbow table")
            return None
        
        # Try each position in the chain
        for pos in range(self.chain_length - 1, -1, -1):
            # Start from the hash and walk the chain
            current_hash = hash_value
            current_pos = pos
            
            while current_pos < self.chain_length - 1:
                # Reduce to get next plaintext
                plain = self._reduce(current_hash, current_pos)
                if not plain:
                    break
                
                # Hash the plaintext
                current_hash = self._hash(plain)
                current_pos += 1
                
                # Check if this hash is an endpoint in our table
                if current_hash in self.chain_map:
                    # Rebuild the chain to find the password
                    return self._rebuild_chain(
                        self.chain_map[current_hash].start,
                        hash_value
                    )
        
        return None
    
    def _rebuild_chain(self, start: str, target_hash: str) -> Optional[str]:
        """Rebuild a chain to find a password"""
        current = start
        
        for i in range(self.chain_length):
            # Hash the current value
            h = self._hash(current)
            
            # Check if we found the target hash
            if h == target_hash:
                return current
            
            # Reduce to get next plaintext
            current = self._reduce(h, i)
            if not current:
                break
        
        return None
    
    def _hash(self, data: str) -> str:
        """Hash a string using the configured algorithm"""
        h = hashlib.new(self.algorithm)
        h.update(data.encode('utf-8'))
        return h.hexdigest()
    
    def _reduce(self, hash_value: str, position: int) -> str:
        """Reduce a hash to a plaintext string"""
        # Simple reduction function - can be improved
        try:
            # Use the hash to seed a deterministic RNG
            seed = int(hash_value[:8], 16) + position
            rng = self._get_rng(seed)
            
            # Generate a password of random length (4-12 characters)
            length = 4 + (rng.randint(0, 1000) % 9)
            password = []
            
            for _ in range(length):
                idx = rng.randint(0, len(self.charset) - 1)
                password.append(self.charset[idx])
            
            return ''.join(password)
        except Exception as e:
            logger.error(f"Reduction failed: {e}")
            return None
    
    @staticmethod
    def _get_rng(seed: int):
        """Get a deterministic RNG"""
        import random
        rng = random.Random()
        rng.seed(seed)
        return rng
    
    @staticmethod
    def _random_string(length: int) -> str:
        """Generate a random string"""
        import random
        import string
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(length))
    
    def save(self, file_path: str) -> None:
        """Save the rainbow table to a file"""
        data = {
            'algorithm': self.algorithm,
            'chain_length': self.chain_length,
            'table_size': self.table_size,
            'charset': self.charset,
            'chains': [asdict(chain) for chain in self.chains]
        }
        
        with open(file_path, 'wb') as f:
            pickle.dump(data, f)
        
        logger.info(f"Saved rainbow table to {file_path}")
    
    @classmethod
    def load(cls, file_path: str) -> 'RainbowTable':
        """Load a rainbow table from a file"""
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        rt = cls(
            algorithm=data['algorithm'],
            chain_length=data['chain_length'],
            table_size=data['table_size'],
            charset=data.get('charset')
        )
        
        rt.chains = [
            RainbowTableChain(
                start=chain['start'],
                end=chain['end'],
                length=chain['length']
            )
            for chain in data['chains']
        ]
        
        # Rebuild the chain map
        rt.chain_map = {chain.end: chain for chain in rt.chains}
        rt.loaded = True
        
        logger.info(f"Loaded rainbow table with {len(rt.chains)} chains")
        return rt

# Example usage
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Rainbow Table Generator')
    parser.add_argument('--generate', action='store_true', help='Generate a new rainbow table')
    parser.add_argument('--lookup', help='Hash to look up in the rainbow table')
    parser.add_argument('--table', default='rainbow_table.rt', help='Rainbow table file')
    parser.add_argument('--algorithm', default='md5', help='Hash algorithm to use')
    parser.add_argument('--size', type=int, default=10000, help='Number of chains in the table')
    parser.add_argument('--length', type=int, default=1000, help='Length of each chain')
    
    args = parser.parse_args()
    
    if args.generate:
        rt = RainbowTable(
            algorithm=args.algorithm,
            table_size=args.size,
            chain_length=args.length
        )
        rt.generate(args.table)
    elif args.lookup:
        if not os.path.exists(args.table):
            print(f"Error: Rainbow table {args.table} not found")
        else:
            rt = RainbowTable.load(args.table)
            password = rt.lookup(args.lookup)
            if password:
                print(f"Found password: {password}")
            else:
                print("Password not found in rainbow table")
    else:
        parser.print_help()
