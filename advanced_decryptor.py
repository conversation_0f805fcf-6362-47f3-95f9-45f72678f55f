"""
Advanced Universal Decryptor with GPU acceleration and multiple attack vectors
"""
import os
import logging
import hashlib
from typing import Optional, Dict, Any, Union, BinaryIO
from pathlib import Path

from gpu_accelerator import GPUAccelerator
from key_manager import KeyManager
from archive_extractor import ArchiveExtractor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('AdvancedDecryptor')

class AdvancedDecryptor:
    """Advanced universal decryptor with support for multiple encryption types"""
    
    def __init__(self, output_dir: str = None, use_gpu: bool = True):
        """Initialize the advanced decryptor"""
        self.output_dir = output_dir or os.path.join(os.getcwd(), 'decrypted')
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize components
        self.gpu = GPUAccelerator() if use_gpu else None
        self.key_manager = KeyManager()
        self.archive_extractor = ArchiveExtractor(output_dir=self.output_dir)
        
        logger.info(f"AdvancedDecryptor initialized. Output directory: {self.output_dir}")
        if self.gpu and hasattr(self.gpu, 'initialized') and self.gpu.initialized:
            logger.info(f"GPU acceleration: {self.gpu.device.name.decode()}")
    
    def analyze_file(self, file_path: Union[str, BinaryIO, bytes], 
                    file_name: str = None) -> Dict[str, Any]:
        """Analyze a file to detect encryption and other properties"""
        try:
            # Read file data
            if isinstance(file_path, (str, Path)):
                with open(file_path, 'rb') as f:
                    data = f.read()
                file_name = file_name or os.path.basename(file_path)
            elif isinstance(file_path, bytes):
                data = file_path
                file_name = file_name or 'unknown.bin'
            else:  # File-like object
                data = file_path.read()
                file_name = file_name or getattr(file_path, 'name', 'unknown.bin')
            
            # Initialize result
            result = {
                'file_name': file_name,
                'file_size': len(data),
                'is_encrypted': False,
                'is_archive': False,
                'possible_algorithms': []
            }
            
            # Check if it's an archive
            archive_info = self.archive_extractor.detect_archive(data)
            if archive_info:
                result.update({
                    'is_archive': True,
                    'archive_type': archive_info.get('type'),
                    'password_protected': archive_info.get('encrypted', False)
                })
            
            # Check for encryption
            if self._is_likely_encrypted(data):
                result['is_encrypted'] = True
                result['possible_algorithms'] = self._detect_encryption_algorithms(data)
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing file: {e}", exc_info=True)
            raise
    
    def _is_likely_encrypted(self, data: bytes) -> bool:
        """Check if data is likely encrypted"""
        # Simple entropy check
        entropy = self._calculate_entropy(data)
        return entropy > 7.0  # High entropy suggests encryption
    
    @staticmethod
    def _calculate_entropy(data: bytes) -> float:
        """Calculate the Shannon entropy of the data"""
        if not data:
            return 0.0
            
        entropy = 0.0
        for x in range(256):
            p_x = float(data.count(x)) / len(data)
            if p_x > 0:
                entropy += -p_x * (p_x.bit_length() - (1 + p_x.bit_length()).bit_length())
        return entropy
    
    def _detect_encryption_algorithms(self, data: bytes) -> list:
        """Detect possible encryption algorithms"""
        # This is a simplified implementation
        # In a real system, this would analyze file signatures, headers, etc.
        return ['AES', 'DES', '3DES']  # Placeholder

# Example usage
if __name__ == "__main__":
    # Initialize the decryptor
    decryptor = AdvancedDecryptor()
    
    # Example: Analyze a file
    analysis = decryptor.analyze_file("example.enc")
    print(f"File analysis: {analysis}")
    
    # Example: Decrypt a file
    if analysis.get('is_encrypted'):
        result = decryptor.decrypt_file(
            "example.enc",
            password="mypassword",
            algorithm=analysis['possible_algorithms'][0] if analysis['possible_algorithms'] else None
        )
        print(f"Decryption result: {result}")
