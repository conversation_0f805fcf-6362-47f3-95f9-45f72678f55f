# Makefile لخوارزمية التشفير المتقدمة
# Advanced Quantum-Resistant Encryption Algorithm Makefile

.PHONY: help install test examples clean benchmark demo setup

# المتغيرات
PYTHON = python
PIP = pip
VENV = venv
REQUIREMENTS = requirements.txt

# الهدف الافتراضي
help:
	@echo "🔐 خوارزمية التشفير المتقدمة - الأوامر المتاحة:"
	@echo "=================================================="
	@echo "setup          - إعداد البيئة وتثبيت المتطلبات"
	@echo "install        - تثبيت المتطلبات"
	@echo "test           - تشغيل الاختبارات"
	@echo "examples       - تشغيل الأمثلة"
	@echo "benchmark      - قياس الأداء"
	@echo "demo           - عرض توضيحي شامل"
	@echo "clean          - تنظيف الملفات المؤقتة"
	@echo "help           - عرض هذه المساعدة"

# إعداد البيئة الافتراضية
setup:
	@echo "🚀 إعداد البيئة..."
	$(PYTHON) -m venv $(VENV)
	@echo "📦 تثبيت المتطلبات..."
	$(VENV)/Scripts/$(PIP) install -r $(REQUIREMENTS)
	@echo "✅ تم إعداد البيئة بنجاح!"
	@echo "💡 لتفعيل البيئة الافتراضية:"
	@echo "   Windows: $(VENV)\\Scripts\\activate"
	@echo "   Linux/Mac: source $(VENV)/bin/activate"

# تثبيت المتطلبات
install:
	@echo "📦 تثبيت المتطلبات..."
	$(PIP) install -r $(REQUIREMENTS)
	@echo "✅ تم تثبيت المتطلبات بنجاح!"

# تشغيل الاختبارات
test:
	@echo "🧪 تشغيل الاختبارات..."
	$(PYTHON) test_crypto.py
	@echo "✅ انتهت الاختبارات!"

# تشغيل الأمثلة
examples:
	@echo "📚 تشغيل الأمثلة..."
	$(PYTHON) crypto_examples.py
	@echo "✅ انتهت الأمثلة!"

# قياس الأداء
benchmark:
	@echo "📊 قياس أداء التشفير..."
	$(PYTHON) crypto_cli.py benchmark -s 1048576
	@echo "✅ انتهى قياس الأداء!"

# عرض توضيحي شامل
demo: test examples benchmark
	@echo "🎉 انتهى العرض التوضيحي الشامل!"

# تنظيف الملفات المؤقتة
clean:
	@echo "🧹 تنظيف الملفات المؤقتة..."
	@if exist __pycache__ rmdir /s /q __pycache__
	@if exist *.pyc del *.pyc
	@if exist *.pyo del *.pyo
	@if exist *.encrypted del *.encrypted
	@if exist *.decrypted del *.decrypted
	@if exist test_*.txt del test_*.txt
	@if exist private_key.pem del private_key.pem
	@if exist public_key.pem del public_key.pem
	@echo "✅ تم التنظيف!"

# أوامر سطر الأوامر
encrypt-demo:
	@echo "🔒 مثال على التشفير..."
	@echo "هذا ملف تجريبي للتشفير" > demo.txt
	$(PYTHON) crypto_cli.py encrypt demo.txt -p "demo_password"
	@echo "✅ تم تشفير demo.txt"

decrypt-demo:
	@echo "🔓 مثال على فك التشفير..."
	$(PYTHON) crypto_cli.py decrypt demo.txt.encrypted -p "demo_password"
	@echo "✅ تم فك تشفير الملف"

generate-keys-demo:
	@echo "🔑 مثال على توليد المفاتيح..."
	$(PYTHON) crypto_cli.py generate-keys
	@echo "✅ تم توليد المفاتيح"

generate-password-demo:
	@echo "🔐 مثال على توليد كلمة مرور..."
	$(PYTHON) crypto_cli.py generate-password -l 32
	@echo "✅ تم توليد كلمة مرور آمنة"

# تشغيل جميع أمثلة سطر الأوامر
cli-demo: generate-password-demo generate-keys-demo encrypt-demo decrypt-demo
	@echo "🎉 انتهت أمثلة سطر الأوامر!"

# تشغيل كامل
full-demo: demo cli-demo
	@echo "🏆 انتهى العرض التوضيحي الكامل!"
	@echo "🔐 خوارزمية التشفير جاهزة للاستخدام!"
