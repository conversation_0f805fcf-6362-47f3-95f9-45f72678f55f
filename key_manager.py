"""
Advanced Key Management for cryptographic operations
"""
import os
import json
import logging
import hashlib
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

from Crypto.PublicKey import RSA, ECC
from Crypto.Cipher import PKCS1_OAEP

logger = logging.getLogger('KeyManager')

@dataclass
class KeyInfo:
    """Information about a cryptographic key"""
    key_id: str
    key_type: str  # 'rsa', 'ecc'
    key_size: int
    public_key: str
    private_key: Optional[str] = None
    created_at: str = None

class KeyManager:
    """Manages cryptographic keys and operations"""
    
    def __init__(self, key_store_path: str = None):
        """Initialize the key manager"""
        self.key_store_path = key_store_path or os.path.join(
            os.path.expanduser('~'), '.crypto', 'keys'
        )
        os.makedirs(self.key_store_path, exist_ok=True)
        self.keys: Dict[str, KeyInfo] = {}
        self._load_keys()
    
    def _load_keys(self) -> None:
        """Load keys from the key store"""
        self.keys = {}
        for key_file in Path(self.key_store_path).glob('*.json'):
            try:
                with open(key_file, 'r') as f:
                    key_data = json.load(f)
                key_info = KeyInfo(**key_data)
                self.keys[key_info.key_id] = key_info
            except Exception as e:
                logger.error(f"Failed to load key from {key_file}: {e}")
    
    def _save_key(self, key_info: KeyInfo) -> None:
        """Save a key to the key store"""
        key_file = os.path.join(self.key_store_path, f"{key_info.key_id}.json")
        try:
            with open(key_file, 'w') as f:
                json.dump(asdict(key_info), f, indent=2)
            self._load_keys()
        except Exception as e:
            logger.error(f"Failed to save key {key_info.key_id}: {e}")
            raise
    
    def generate_rsa_key(self, key_size: int = 4096, key_id: str = None) -> KeyInfo:
        """Generate a new RSA key pair"""
        try:
            key = RSA.generate(key_size)
            key_id = key_id or f"rsa_{key_size}_{hashlib.sha256(key.publickey().export_key()).hexdigest()[:8]}"
            
            key_info = KeyInfo(
                key_id=key_id,
                key_type='rsa',
                key_size=key_size,
                public_key=key.publickey().export_key().decode('utf-8'),
                private_key=key.export_key().decode('utf-8'),
                created_at=str(os.path.getctime(key_id + '.json'))
            )
            
            self._save_key(key_info)
            return key_info
        except Exception as e:
            logger.error(f"Failed to generate RSA key: {e}")
            raise
    
    def get_key(self, key_id: str) -> Optional[KeyInfo]:
        """Get a key by ID"""
        return self.keys.get(key_id)
    
    def list_keys(self) -> List[KeyInfo]:
        """List all available keys"""
        return list(self.keys.values())
    
    def delete_key(self, key_id: str) -> bool:
        """Delete a key from the key store"""
        if key_id not in self.keys:
            return False
            
        try:
            key_file = os.path.join(self.key_store_path, f"{key_id}.json")
            if os.path.exists(key_file):
                os.remove(key_file)
            del self.keys[key_id]
            return True
        except Exception as e:
            logger.error(f"Failed to delete key {key_id}: {e}")
            return False
    
    def rsa_decrypt(self, key_id: str, ciphertext: bytes) -> Optional[bytes]:
        """Decrypt data using an RSA private key"""
        if key_id not in self.keys or self.keys[key_id].key_type != 'rsa':
            logger.error(f"RSA key {key_id} not found")
            return None
            
        try:
            key_info = self.keys[key_id]
            if not key_info.private_key:
                logger.error(f"No private key available for {key_id}")
                return None
            
            key = RSA.import_key(key_info.private_key)
            cipher = PKCS1_OAEP.new(key)
            return cipher.decrypt(ciphertext)
            
        except Exception as e:
            logger.error(f"RSA decryption failed: {e}")
            return None

# Example usage
if __name__ == "__main__":
    km = KeyManager()
    
    # Generate a new RSA key
    key = km.generate_rsa_key(4096)
    print(f"Generated key: {key.key_id}")
    
    # List all keys
    print("\nAvailable keys:")
    for k in km.list_keys():
        print(f"- {k.key_id} ({k.key_type} {k.key_size} bits)")
