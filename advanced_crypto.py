#!/usr/bin/env python3
"""
خوارزمية التشفير المتقدمة - Advanced Quantum-Resistant Encryption Algorithm
نظام تشفير هجين متعدد الطبقات مع مقاومة الحوسبة الكمية

المميزات:
- تشفير هجين (AES-256-GCM + RSA-4096)
- تشفير متعدد الطبقات مع XOR ديناميكي
- مقاومة الهجمات الكمية
- تحقق من سلامة البيانات متعدد المستويات
- إدارة مفاتيح متقدمة
"""

import os
import hashlib
import hmac
import secrets
import base64
import json
from typing import Tuple, Dict, Any, Optional
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes, serialization, padding
from cryptography.hazmat.primitives.asymmetric import rsa, padding as asym_padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
import time


class QuantumResistantCrypto:
    """خوارزمية التشفير المقاومة للحوسبة الكمية"""
    
    def __init__(self, master_password: str = None):
        self.backend = default_backend()
        self.master_password = master_password
        self.key_size = 32  # 256 bits
        self.iv_size = 16   # 128 bits
        self.salt_size = 32 # 256 bits
        self.tag_size = 16  # 128 bits
        
    def _generate_dynamic_salt(self, data: bytes, timestamp: float) -> bytes:
        """توليد ملح ديناميكي بناءً على البيانات والوقت"""
        dynamic_input = data[:64] + str(timestamp).encode() + secrets.token_bytes(16)
        return hashlib.sha3_256(dynamic_input).digest()
    
    def _derive_key_pbkdf2(self, password: str, salt: bytes, iterations: int = 100000) -> bytes:
        """اشتقاق مفتاح باستخدام PBKDF2 مع SHA3-512"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA3_512(),
            length=self.key_size,
            salt=salt,
            iterations=iterations,
            backend=self.backend
        )
        return kdf.derive(password.encode())
    
    def _multi_layer_xor(self, data: bytes, key: bytes) -> bytes:
        """تشفير XOR متعدد الطبقات مع مفاتيح ديناميكية - قابل للعكس"""
        if len(data) == 0:
            return data

        result = bytearray(data)
        key_extended = (key * ((len(data) // len(key)) + 1))[:len(data)]

        # الطبقة الأولى: XOR أساسي
        for i in range(len(result)):
            result[i] ^= key_extended[i]

        # الطبقة الثانية: XOR مع تحويل موضعي (قابل للعكس)
        for i in range(len(result)):
            shift_key = (key_extended[i] + i) % 256
            result[i] ^= shift_key

        return bytes(result)
    
    def _aes_gcm_encrypt(self, data: bytes, key: bytes) -> Tuple[bytes, bytes, bytes]:
        """تشفير AES-256-GCM"""
        iv = secrets.token_bytes(self.iv_size)
        cipher = Cipher(algorithms.AES(key), modes.GCM(iv), backend=self.backend)
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(data) + encryptor.finalize()
        return ciphertext, iv, encryptor.tag
    
    def _aes_gcm_decrypt(self, ciphertext: bytes, key: bytes, iv: bytes, tag: bytes) -> bytes:
        """فك تشفير AES-256-GCM"""
        cipher = Cipher(algorithms.AES(key), modes.GCM(iv, tag), backend=self.backend)
        decryptor = cipher.decryptor()
        return decryptor.update(ciphertext) + decryptor.finalize()
    
    def generate_rsa_keypair(self, key_size: int = 4096) -> Tuple[bytes, bytes]:
        """توليد زوج مفاتيح RSA-4096"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size,
            backend=self.backend
        )
        
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_key = private_key.public_key()
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return private_pem, public_pem
    
    def _rsa_encrypt(self, data: bytes, public_key_pem: bytes) -> bytes:
        """تشفير RSA مع OAEP padding"""
        public_key = serialization.load_pem_public_key(public_key_pem, backend=self.backend)
        return public_key.encrypt(
            data,
            asym_padding.OAEP(
                mgf=asym_padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def _rsa_decrypt(self, ciphertext: bytes, private_key_pem: bytes) -> bytes:
        """فك تشفير RSA"""
        private_key = serialization.load_pem_private_key(
            private_key_pem, password=None, backend=self.backend
        )
        return private_key.decrypt(
            ciphertext,
            asym_padding.OAEP(
                mgf=asym_padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def _calculate_integrity_hash(self, data: bytes, key: bytes) -> bytes:
        """حساب hash للتحقق من سلامة البيانات باستخدام HMAC-SHA3-512"""
        return hmac.new(key, data, hashlib.sha3_512).digest()
    
    def encrypt_data(self, data: bytes, password: str = None, public_key_pem: bytes = None) -> Dict[str, Any]:
        """
        تشفير البيانات باستخدام النظام الهجين متعدد الطبقات
        
        Args:
            data: البيانات المراد تشفيرها
            password: كلمة المرور (اختيارية)
            public_key_pem: المفتاح العام RSA (اختياري)
        
        Returns:
            قاموس يحتوي على البيانات المشفرة والمعلومات المطلوبة لفك التشفير
        """
        timestamp = time.time()
        
        # استخدام كلمة المرور المحددة أو الافتراضية أو توليد مؤقتة للتشفير الهجين
        used_password = password or self.master_password
        if not used_password and not public_key_pem:
            raise ValueError("يجب تحديد كلمة مرور")
        elif not used_password and public_key_pem:
            # توليد كلمة مرور مؤقتة للتشفير الهجين
            used_password = self.generate_secure_password(32)
        
        # توليد الملح الديناميكي
        salt = self._generate_dynamic_salt(data, timestamp)
        
        # اشتقاق المفتاح الرئيسي
        master_key = self._derive_key_pbkdf2(used_password, salt)
        
        # توليد مفاتيح فرعية للطبقات المختلفة
        xor_key = hashlib.sha3_256(master_key + b"XOR_LAYER").digest()
        aes_key = hashlib.sha3_256(master_key + b"AES_LAYER").digest()
        integrity_key = hashlib.sha3_256(master_key + b"INTEGRITY").digest()
        
        # الطبقة الأولى: تشفير XOR متعدد الطبقات
        layer1_encrypted = self._multi_layer_xor(data, xor_key)
        
        # الطبقة الثانية: تشفير AES-256-GCM
        layer2_encrypted, iv, tag = self._aes_gcm_encrypt(layer1_encrypted, aes_key)
        
        # حساب hash التحقق من السلامة
        integrity_data = salt + iv + tag + layer2_encrypted
        integrity_hash = self._calculate_integrity_hash(integrity_data, integrity_key)
        
        # تجميع البيانات المشفرة
        encrypted_package = {
            "version": "1.0",
            "timestamp": timestamp,
            "salt": base64.b64encode(salt).decode(),
            "iv": base64.b64encode(iv).decode(),
            "tag": base64.b64encode(tag).decode(),
            "ciphertext": base64.b64encode(layer2_encrypted).decode(),
            "integrity_hash": base64.b64encode(integrity_hash).decode(),
            "algorithm": "QuantumResistant-AES256-GCM-RSA4096"
        }
        
        # تشفير إضافي باستخدام RSA إذا تم توفير المفتاح العام
        if public_key_pem:
            # تشفير المفتاح الرئيسي باستخدام RSA
            encrypted_master_key = self._rsa_encrypt(master_key, public_key_pem)
            encrypted_package["rsa_encrypted_key"] = base64.b64encode(encrypted_master_key).decode()
            encrypted_package["uses_rsa"] = True
        else:
            encrypted_package["uses_rsa"] = False
        
        return encrypted_package

    def decrypt_data(self, encrypted_package: Dict[str, Any], password: str = None,
                    private_key_pem: bytes = None) -> bytes:
        """
        فك تشفير البيانات

        Args:
            encrypted_package: الحزمة المشفرة
            password: كلمة المرور
            private_key_pem: المفتاح الخاص RSA (إذا كان مطلوباً)

        Returns:
            البيانات الأصلية بعد فك التشفير
        """
        try:
            # التحقق من إصدار الخوارزمية
            if encrypted_package.get("version") != "1.0":
                raise ValueError("إصدار غير مدعوم من الخوارزمية")

            # استخراج البيانات المشفرة
            salt = base64.b64decode(encrypted_package["salt"])
            iv = base64.b64decode(encrypted_package["iv"])
            tag = base64.b64decode(encrypted_package["tag"])
            ciphertext = base64.b64decode(encrypted_package["ciphertext"])
            stored_integrity_hash = base64.b64decode(encrypted_package["integrity_hash"])

            # تحديد المفتاح الرئيسي
            if encrypted_package.get("uses_rsa", False):
                if not private_key_pem:
                    raise ValueError("مطلوب المفتاح الخاص RSA لفك التشفير")

                encrypted_master_key = base64.b64decode(encrypted_package["rsa_encrypted_key"])
                master_key = self._rsa_decrypt(encrypted_master_key, private_key_pem)
            else:
                used_password = password or self.master_password
                if not used_password:
                    raise ValueError("يجب تحديد كلمة مرور")

                master_key = self._derive_key_pbkdf2(used_password, salt)

            # توليد المفاتيح الفرعية
            xor_key = hashlib.sha3_256(master_key + b"XOR_LAYER").digest()
            aes_key = hashlib.sha3_256(master_key + b"AES_LAYER").digest()
            integrity_key = hashlib.sha3_256(master_key + b"INTEGRITY").digest()

            # التحقق من سلامة البيانات
            integrity_data = salt + iv + tag + ciphertext
            calculated_integrity_hash = self._calculate_integrity_hash(integrity_data, integrity_key)

            if not hmac.compare_digest(stored_integrity_hash, calculated_integrity_hash):
                raise ValueError("فشل في التحقق من سلامة البيانات - البيانات قد تكون تالفة أو مُعدلة")

            # فك التشفير - الطبقة الثانية: AES-256-GCM
            layer1_decrypted = self._aes_gcm_decrypt(ciphertext, aes_key, iv, tag)

            # فك التشفير - الطبقة الأولى: XOR متعدد الطبقات
            original_data = self._multi_layer_xor(layer1_decrypted, xor_key)

            return original_data

        except Exception as e:
            raise ValueError(f"فشل في فك التشفير: {str(e)}")

    def encrypt_file(self, file_path: str, output_path: str = None, password: str = None,
                    public_key_pem: bytes = None) -> str:
        """تشفير ملف"""
        with open(file_path, 'rb') as f:
            data = f.read()

        encrypted_package = self.encrypt_data(data, password, public_key_pem)

        output_file = output_path or f"{file_path}.encrypted"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(encrypted_package, f, indent=2, ensure_ascii=False)

        return output_file

    def decrypt_file(self, encrypted_file_path: str, output_path: str = None,
                    password: str = None, private_key_pem: bytes = None) -> str:
        """فك تشفير ملف"""
        with open(encrypted_file_path, 'r', encoding='utf-8') as f:
            encrypted_package = json.load(f)

        decrypted_data = self.decrypt_data(encrypted_package, password, private_key_pem)

        output_file = output_path or encrypted_file_path.replace('.encrypted', '.decrypted')
        with open(output_file, 'wb') as f:
            f.write(decrypted_data)

        return output_file

    def generate_secure_password(self, length: int = 32) -> str:
        """توليد كلمة مرور آمنة"""
        import string
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*()_+-=[]{}|;:,.<>?"
        return ''.join(secrets.choice(alphabet) for _ in range(length))

    def benchmark_encryption(self, data_size: int = 1024*1024) -> Dict[str, float]:
        """قياس أداء التشفير"""
        test_data = secrets.token_bytes(data_size)
        password = "test_password_123"

        # قياس وقت التشفير
        start_time = time.time()
        encrypted = self.encrypt_data(test_data, password)
        encryption_time = time.time() - start_time

        # قياس وقت فك التشفير
        start_time = time.time()
        decrypted = self.decrypt_data(encrypted, password)
        decryption_time = time.time() - start_time

        # التحقق من صحة العملية
        assert test_data == decrypted, "فشل في التحقق من صحة التشفير/فك التشفير"

        return {
            "data_size_mb": data_size / (1024*1024),
            "encryption_time_seconds": encryption_time,
            "decryption_time_seconds": decryption_time,
            "encryption_speed_mbps": (data_size / (1024*1024)) / encryption_time,
            "decryption_speed_mbps": (data_size / (1024*1024)) / decryption_time,
            "total_time_seconds": encryption_time + decryption_time
        }
