#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Universal File Decryptor - أداة فك التشفير الشاملة

أداة متقدمة لمحاولة فك تشفير الملفات المشفرة باستخدام خوارزميات متعددة.
"""

import os
import sys
import json
import base64
import logging
import hashlib
import binascii
import struct
import magic
import zlib
import gzip
import bz2
import lzma
import zipfile
import rarfile
import py7zr
from typing import Dict, List, Optional, Tuple, Union, Any, BinaryIO
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor
from pathlib import Path
from datetime import datetime

# Third-party imports
try:
    from Crypto.Cipher import AES, DES, DES3, Blowfish, PKCS1_OAEP
    from Crypto.PublicKey import RSA, ECC
    from Crypto.Util.Padding import unpad
    from Crypto.Random import get_random_bytes
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    print("Warning: PyCryptodome not found. Some decryption features may be limited.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('decryptor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UniversalDecryptor:
    """فئة فك التشفير الشاملة التي تدعم خوارزميات متعددة"""
    
    def __init__(self, max_workers: int = 4):
        """تهيئة فك التشفير"""
        self.max_workers = max_workers
        self.supported_algorithms = self._get_supported_algorithms()
        self.common_passwords = self._load_common_passwords()
        self.magic = magic.Magic(mime=True, mime_encoding=True)
    
    def _get_supported_algorithms(self) -> Dict[str, dict]:
        """الحصول على قائمة خوارزميات التشفير المدعومة"""
        algs = {
            'aes': {
                'name': 'AES',
                'key_sizes': [128, 192, 256],
                'modes': ['CBC', 'GCM', 'ECB', 'CFB', 'OFB', 'CTR'],
                'detection': self._detect_aes_encryption,
                'decrypt': self._decrypt_aes
            },
            'rsa': {
                'name': 'RSA',
                'key_sizes': [1024, 2048, 3072, 4096],
                'detection': self._detect_rsa_encryption,
                'decrypt': self._decrypt_rsa
            },
            'xor': {
                'name': 'XOR',
                'key_sizes': [1, 2, 4, 8, 16, 32, 64, 128],
                'detection': self._detect_xor_encryption,
                'decrypt': self._decrypt_xor
            },
            'des': {
                'name': 'DES',
                'key_sizes': [56],
                'modes': ['CBC', 'ECB', 'CFB', 'OFB'],
                'detection': self._detect_des_encryption,
                'decrypt': self._decrypt_des
            },
            '3des': {
                'name': '3DES',
                'key_sizes': [112, 168],
                'modes': ['CBC', 'ECB'],
                'detection': self._detect_3des_encryption,
                'decrypt': self._decrypt_3des
            },
            'blowfish': {
                'name': 'Blowfish',
                'key_sizes': [32, 64, 128, 256, 448],
                'modes': ['CBC', 'ECB'],
                'detection': self._detect_blowfish_encryption,
                'decrypt': self._decrypt_blowfish
            },
            'chacha20': {
                'name': 'ChaCha20',
                'key_sizes': [256],
                'detection': self._detect_chacha20_encryption,
                'decrypt': self._decrypt_chacha20
            },
            'serpent': {
                'name': 'Serpent',
                'key_sizes': [128, 192, 256],
                'modes': ['CBC', 'CTR'],
                'detection': self._detect_serpent_encryption,
                'decrypt': self._decrypt_serpent
            },
            'twofish': {
                'name': 'Twofish',
                'key_sizes': [128, 192, 256],
                'modes': ['CBC', 'CTR'],
                'detection': self._detect_twofish_encryption,
                'decrypt': self._decrypt_twofish
            },
            'arc4': {
                'name': 'RC4',
                'key_sizes': [40, 56, 64, 80, 128, 192, 256],
                'detection': self._detect_rc4_encryption,
                'decrypt': self._decrypt_rc4
            }
        }
        
        # Add compression algorithms
        algs.update({
            'gzip': {
                'name': 'GZIP',
                'detection': self._detect_gzip_compression,
                'decompress': self._decompress_gzip
            },
            'bzip2': {
                'name': 'BZIP2',
                'detection': self._detect_bzip2_compression,
                'decompress': self._decompress_bzip2
            },
            'lzma': {
                'name': 'LZMA',
                'detection': self._detect_lzma_compression,
                'decompress': self._decompress_lzma
            },
            'zip': {
                'name': 'ZIP',
                'detection': self._detect_zip_archive,
                'extract': self._extract_zip
            },
            'rar': {
                'name': 'RAR',
                'detection': self._detect_rar_archive,
                'extract': self._extract_rar
            },
            '7z': {
                'name': '7-Zip',
                'detection': self._detect_7z_archive,
                'extract': self._extract_7z
            }
        })
        
        return algs
    
    def _load_common_passwords(self) -> List[str]:
        """تحميل قائمة بكلمات المرور الشائعة"""
        common = [
            'password', '123456', '12345678', '1234', 'qwerty', '12345',
            'dragon', 'baseball', 'football', 'letmein', 'monkey', 'abc123',
            'mustang', 'michael', 'shadow', 'master', 'jennifer', '111111',
            '2000', 'jordan', 'superman', 'harley', '1234567', 'freedom',
            'charlie', 'trustno1', 'robert', 'soccer', 'hunter', 'basketball'
        ]
        return common

    # Detection methods for different encryption types
    def _detect_aes_encryption(self, file_path: str) -> Dict[str, Any]:
        """كشف تشفير AES"""
        # Implementation would analyze file for AES patterns
        return {'confidence': 0, 'details': 'AES detection not implemented'}
    
    def _detect_rsa_encryption(self, file_path: str) -> Dict[str, Any]:
        """كشف تشفير RSA"""
        # Implementation would analyze file for RSA patterns
        return {'confidence': 0, 'details': 'RSA detection not implemented'}
    
    # Add similar detection methods for other algorithms
    
    # Decryption methods
    def _decrypt_aes(self, file_path: str, key: bytes, iv: bytes = None, mode: str = 'CBC') -> Optional[bytes]:
        """فك تشفير AES"""
        if not CRYPTO_AVAILABLE:
            logger.error("PyCryptodome is required for AES decryption")
            return None
            
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
                
            if mode.upper() == 'CBC':
                cipher = AES.new(key, AES.MODE_CBC, iv or bytes(16))
                decrypted = unpad(cipher.decrypt(data), AES.block_size)
                return decrypted
                
            # Add other modes as needed
            
        except Exception as e:
            logger.error(f"فشل فك تشفير AES: {str(e)}")
            return None
    
    # Add similar decryption methods for other algorithms
    
    # Compression handling
    def _decompress_gzip(self, file_path: str) -> Optional[bytes]:
        """فك ضغط GZIP"""
        try:
            with gzip.open(file_path, 'rb') as f:
                return f.read()
        except Exception as e:
            logger.error(f"فشل فك ضغط GZIP: {str(e)}")
            return None
    
    # Add similar decompression methods for other formats
    
    # Main decryption interface
    def decrypt_file(self, file_path: str, output_path: str = None, 
                    password: str = None, algorithm: str = None) -> bool:
        """واجهة فك التشفير الرئيسية"""
        if not os.path.exists(file_path):
            logger.error(f"الملف غير موجود: {file_path}")
            return False
            
        # If no specific algorithm specified, try to detect
        if not algorithm:
            file_info = self.analyze_file(file_path)
            if not file_info.get('is_encrypted', False):
                logger.warning("لا يبدو أن الملف مشفر أو لا يمكن تحديد نوع التشفير")
                return False
                
            # Try detected algorithms in order of confidence
            for algo in sorted(file_info['detected_algorithms'], 
                             key=lambda x: x['confidence'], reverse=True):
                result = self._try_decrypt_with_algorithm(
                    file_path, output_path, algo['algorithm'], password)
                if result:
                    return True
        else:
            return self._try_decrypt_with_algorithm(
                file_path, output_path, algorithm, password)
                
        return False
    
    def _try_decrypt_with_algorithm(self, file_path: str, output_path: str, 
                                  algorithm: str, password: str = None) -> bool:
        """محاولة فك التشفير باستخدام خوارزمية محددة"""
        logger.info(f"محاولة فك التشفير باستخدام {algorithm}...")
        
        # Implementation would try decryption with the specified algorithm
        # and return True if successful
        
        return False

    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """تحليل الملف لتحديد نوع التشفير المحتمل"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"الملف غير موجود: {file_path}")
        
        file_info = {
            'path': file_path,
            'size': os.path.getsize(file_path),
            'detected_algorithms': [],
            'entropy': self._calculate_entropy(file_path),
            'is_encrypted': False,
            'possible_encryption': [],
            'file_type': self.magic.from_file(file_path),
            'analysis_time': datetime.now().isoformat()
        }
        
        # Check for known file signatures
        signature = self._check_file_signature(file_path)
        if signature:
            file_info['signature'] = signature
        
        # Check for compression/archive formats first
        for algo_name in ['gzip', 'bzip2', 'lzma', 'zip', 'rar', '7z']:
            algo = self.supported_algorithms.get(algo_name)
            if algo and 'detection' in algo:
                result = algo['detection'](file_path)
                if result.get('detected', False):
                    file_info['detected_algorithms'].append({
                        'algorithm': algo_name,
                        'name': algo['name'],
                        'confidence': result.get('confidence', 0.8),
                        'details': result.get('details', {})
                    })
        
        # Check for encryption if no compression detected
        if not file_info['detected_algorithms']:
            for algo_name, algo_info in self.supported_algorithms.items():
                if algo_name in ['gzip', 'bzip2', 'lzma', 'zip', 'rar', '7z']:
                    continue  # Skip compression formats we already checked
                    
                if 'detection' in algo_info:
                    result = algo_info['detection'](file_path)
                    if result.get('confidence', 0) > 0.3:  # Threshold for detection
                        file_info['detected_algorithms'].append({
                            'algorithm': algo_name,
                            'name': algo_info['name'],
                            'confidence': result['confidence'],
                            'details': result.get('details', {})
                        })
                        file_info['is_encrypted'] = True
        
        # Check entropy for unknown encryption
        if file_info['entropy'] > 7.0 and not file_info['detected_algorithms']:
            file_info['is_encrypted'] = True
            file_info['possible_encryption'].append({
                'algorithm': 'unknown',
                'confidence': 0.7,
                'details': 'High entropy suggests possible encryption',
                'entropy': file_info['entropy']
            })
        
        return file_info
    
    def _calculate_entropy(self, file_path: str, block_size: int = 4096) -> float:
        """حساب إنتروبيا الملف"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read(block_size)
                
            if not data:
                return 0.0
                
            entropy = 0.0
            for x in range(256):
                p_x = float(data.count(x)) / len(data)
                if p_x > 0:
                    entropy += -p_x * (p_x.bit_length() - 1) / 0.6931471805599453
                    
            return round(entropy, 2)
            
        except Exception as e:
            logger.error(f"Error calculating entropy: {str(e)}")
            return 0.0
    
    def _check_file_signature(self, file_path: str) -> Optional[Dict[str, Any]]:
        """التحقق من توقيع الملف المعروف"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(16)  # Read first 16 bytes for signature
                
            if not header:
                return None
                
            # Common file signatures (magic numbers)
            signatures = {
                b'\x50\x4B\x03\x04': 'ZIP',
                b'\x50\x4B\x05\x06': 'ZIP (empty archive)',
                b'\x50\x4B\x07\x08': 'ZIP (spanned)',
                b'\x52\x61\x72\x21\x1A\x07\x00': 'RAR',
                b'\x52\x61\x72\x21\x1A\x07\x01\x00': 'RAR5',
                b'\x37\x7A\xBC\xAF\x27\x1C': '7z',
                b'\x1F\x8B': 'GZIP',
                b'\x42\x5A\x68': 'BZIP2',
                b'\xFD\x37\x7A\x58\x5A\x00': 'XZ',
                b'\x75\x73\x74\x61\x72': 'TAR',
                b'PK\x03\x04': 'OpenDocument',
                b'D0\xCF\x11\xE0\xA1\xB1\x1A\xE1': 'Microsoft Office',
                b'\x25\x50\x44\x46': 'PDF',
                b'\x7F\x45\x4C\x46': 'ELF',
                b'\x4D\x5A': 'Windows PE',
                b'\xCA\xFE\xBA\xBE': 'Java class',
                b'\xEF\xBB\xBF': 'UTF-8 with BOM',
                b'\xFF\xFE': 'UTF-16/32, little-endian',
                b'\xFE\xFF': 'UTF-16/32, big-endian',
                b'\x00\x00\xFE\xFF': 'UTF-32, big-endian',
                b'\xFF\xFE\x00\x00': 'UTF-32, little-endian'
            }
            
            for sig, file_type in signatures.items():
                if header.startswith(sig):
                    return {
                        'type': file_type,
                        'signature': binascii.hexlify(sig).decode('ascii'),
                        'offset': 0
                    }
                    
            return None
            
        except Exception as e:
            logger.error(f"Error checking file signature: {str(e)}")
            return None

# Command-line interface
def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Universal File Decryptor')
    parser.add_argument('file', help='File to analyze/decrypt')
    parser.add_argument('-o', '--output', help='Output file path')
    parser.add_argument('-p', '--password', help='Password to use for decryption')
    parser.add_argument('-a', '--algorithm', help='Specific algorithm to try')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    decryptor = UniversalDecryptor()
    
    # If output is not specified, create a default name
    if not args.output and args.file:
        base_name = os.path.splitext(args.file)[0]
        args.output = f"{base_name}_decrypted"
    
    # Analyze the file first
    print(f"\nAnalyzing file: {args.file}")
    file_info = decryptor.analyze_file(args.file)
    
    print(f"\nFile Information:")
    print(f"  Size: {file_info['size']} bytes")
    print(f"  Type: {file_info.get('file_type', 'Unknown')}")
    print(f"  Entropy: {file_info['entropy']} (higher values may indicate encryption)")
    
    if file_info.get('signature'):
        print(f"  Signature: {file_info['signature']['type']} (0x{file_info['signature']['signature']})")
    
    if file_info['detected_algorithms']:
        print("\nDetected Algorithms:")
        for algo in file_info['detected_algorithms']:
            print(f"  - {algo['name']} ({algo['algorithm']}): {algo['confidence']*100:.1f}% confidence")
    
    if file_info.get('is_encrypted', False) or args.algorithm:
        print("\nAttempting decryption...")
        success = decryptor.decrypt_file(
            args.file, 
            output_path=args.output,
            password=args.password,
            algorithm=args.algorithm
        )
        
        if success:
            print(f"\nSuccess! Decrypted file saved to: {args.output}")
        else:
            print("\nFailed to decrypt the file with the available methods.")
    else:
        print("\nNo encryption detected. Use --algorithm to force decryption with a specific algorithm.")

if __name__ == "__main__":
    main()