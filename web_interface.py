""\nAdvanced Web Interface for Ultimate Decryption Suite\n\nFeatures:\n- Modern, responsive web UI\n- File upload and analysis\n- Real-time decryption progress\n- Support for all encryption types\n- Advanced attack configuration\n- Result visualization\n"""\n\nfrom fastapi import FastAPI, File, UploadFile, Form, HTTPException, Request\nfrom fastapi.responses import HTMLResponse, JSONResponse, FileResponse\nfrom fastapi.staticfiles import StaticFiles\nfrom fastapi.templating import Jinja2Templates\nfrom pathlib import Path\nimport uvicorn\nimport shutil\nimport tempfile\nimport os\nimport json\nimport asyncio\nfrom typing import Dict, Any, List, Optional\nimport logging\n\nfrom universal_decryptor_advanced import UltimateDecryptor, DecryptionMethod\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger('WebInterface')\n\napp = FastAPI(\n    title="Ultimate Decryption Suite",\n    description="World's most advanced decryption tool",\n    version="1.0.0"\n)\n\n# Create necessary directories\nUPLOAD_DIR = "uploads"\nRESULTS_DIR = "results"\nSTATIC_DIR = "static"\nTEMPLATES_DIR = "templates"\n\nfor directory in [UPLOAD_DIR, RESULTS_DIR, STATIC_DIR, TEMPLATES_DIR]:\n    os.makedirs(directory, exist_ok=True)\n\n# Mount static files\napp.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")\n\n# Setup templates\ntemplates = Jinja2Templates(directory=TEMPLATES_DIR)\n\n# Initialize decryptor\ndecryptor = UltimateDecryptor(use_gpu=True)\n\n# Store active tasks\ntasks: Dict[str, Any] = {}\n\n# HTML Templates\nHTML_TEMPLATE = """\n<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>Ultimate Decryption Suite</title>\n    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">\n    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">\n    <style>\n        body {\n            background-color: #f8f9fa;\n            padding-top: 2rem;\n        }\n        .card {\n            margin-bottom: 2rem;\n            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n        }\n        .progress {\n            height: 25px;\n            margin: 1rem 0;\n        }\n        .result-item {\n            cursor: pointer;\n            transition: background-color 0.2s;\n        }\n        .result-item:hover {\n            background-color: #f8f9fa;\n        }\n        .file-upload {\n            border: 2px dashed #dee2e6;\n            border-radius: 8px;\n            padding: 2rem;\n            text-align: center;\n            cursor: pointer;\n            transition: all 0.3s;\n        }\n        .file-upload:hover {\n            border-color: #0d6efd;\n            background-color: #f8f9ff;\n        }\n        .file-upload input[type="file"] {\n            display: none;\n        }\n    </style>\n</head>\n<body>\n    <div class="container">\n        <div class="row justify-content-center">\n            <div class="col-lg-10">\n                <div class="card">\n                    <div class="card-header bg-primary text-white">\n                        <h2 class="h4 mb-0">Ultimate Decryption Suite</h2>\n                        <p class="mb-0">World's most advanced decryption tool</p>\n                    </div>\n                    <div class="card-body">\n                        <div id="upload-section">\n                            <div class="file-upload mb-4" id="drop-zone">\n                                <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-primary"></i>\n                                <h4>Drag & Drop your encrypted file here</h4>\n                                <p class="text-muted">or click to browse files (ZIP, RAR, 7z, encrypted files)</p>\n                                <input type="file" id="file-input" class="form-control">\n                            </div>\n                            \n                            <div id="file-info" class="d-none">\n                                <div class="alert alert-info">\n                                    <i class="fas fa-file-alt me-2"></i>\n                                    <span id="file-name"></span>\n                                    <span id="file-size" class="text-muted ms-2"></span>\n                                    <button id="change-file" class="btn btn-sm btn-outline-secondary ms-2">Change</button>\n                                </div>\n                                \n                                <div class="mb-3">\n                                    <label class="form-label">Decryption Method</label>\n                                    <select class="form-select" id="decryption-method">\n                                        <option value="auto">Auto-detect (Recommended)</option>\n                                        <option value="dictionary">Dictionary Attack</option>\n                                        <option value="bruteforce">Brute Force</option>\n                                        <option value="rainbow">Rainbow Table</option>\n                                        <option value="ai">AI Prediction</option>\n                                        <option value="hybrid">Hybrid Attack</option>\n                                    </select>\n                                </div>\n                                \n                                <div id="advanced-options" class="mb-3">\n                                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#advancedOptions">\n                                        <i class="fas fa-cog me-1"></i> Advanced Options\n                                    </button>\n                                    \n                                    <div class="collapse mt-3" id="advancedOptions">\n                                        <div class="card card-body">\n                                            <div class="mb-3">\n                                                <label class="form-label">Wordlist</label>\n                                                <select class="form-select" id="wordlist">\n                                                    <option value="default">Default (Common Passwords)</option>\n                                                    <option value="rockyou">RockYou (If available)</option>\n                                                    <option value="custom">Custom (Upload)</option>\n                                                </select>\n                                                <input type="file" id="custom-wordlist" class="form-control mt-2 d-none" accept=".txt">\n                                            </div>\n                                            \n                                            <div class="row">\n                                                <div class="col-md-6">\n                                                    <label class="form-label">Min Length</label>\n                                                    <input type="number" class="form-control" id="min-length" value="4" min="1" max="32">\n                                                </div>\n                                                <div class="col-md-6">\n                                                    <label class="form-label">Max Length</label>\n                                                    <input type="number" class="form-control" id="max-length" value="8" min="1" max="64">\n                                                </div>\n                                            </div>\n                                            \n                                            <div class="form-check form-switch mt-3">\n                                                <input class="form-check-input" type="checkbox" id="use-gpu" checked>\n                                                <label class="form-check-label" for="use-gpu">Use GPU Acceleration</label>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                                \n                                <button id="start-decrypt" class="btn btn-primary w-100 py-2">\n                                    <i class="fas fa-unlock me-2"></i> Start Decryption\n                                </button>\n                            </div>\n                        </div>\n                        \n                        <div id="progress-section" class="d-none">\n                            <h4 class="mb-3">Decryption in Progress</h4>\n                            <div class="progress">\n                                <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%">0%</div>\n                            </div>\n                            <div class="d-flex justify-content-between mb-3">\n                                <small id="progress-status">Initializing...</small>\n                                <small id="progress-speed"></small>\n                            </div>\n                            <div id="progress-details" class="mb-3">\n                                <div class="row">\n                                    <div class="col-md-6">\n                                        <div class="card mb-3">\n                                            <div class="card-body">\n                                                <h6 class="card-subtitle mb-2 text-muted">Attempts</h6>\n                                                <h3 id="attempts" class="card-title">0</h3>\n                                            </div>\n                                        </div>\n                                    </div>\n                                    <div class="col-md-6">\n                                        <div class="card mb-3">\n                                            <div class="card-body">\n                                                <h6 class="card-subtitle mb-2 text-muted">Speed</h6>\n                                                <h3 id="speed" class="card-title">0</h3>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                            <button id="stop-decrypt" class="btn btn-danger">\n                                <i class="fas fa-stop me-1"></i> Stop Decryption\n                            </button>\n                        </div>\n                        \n                        <div id="result-section" class="d-none">\n                            <div id="success-result" class="d-none">\n                                <div class="alert alert-success">\n                                    <h4 class="alert-heading"><i class="fas fa-check-circle me-2"></i>Success!</h4>\n                                    <p>File decrypted successfully in <span id="decryption-time"></span> seconds.</p>\n                                    <hr>\n                                    <p class="mb-0">Password found: <strong id="found-password"></strong></p>\n                                </div>\n                                \n                                <div class="card mb-4">\n                                    <div class="card-header">\n                                        <h5 class="mb-0">Extracted Files</h5>\n                                    </div>\n                                    <div class="list-group list-group-flush" id="file-list">\n                                        <!-- Files will be listed here -->\n                                    </div>\n                                </div>\n                                \n                                <div class="d-grid gap-2">\n                                    <button id="download-all" class="btn btn-primary">\n                                        <i class="fas fa-download me-1"></i> Download All\n                                    </button>\n                                    <button id="new-decrypt" class="btn btn-outline-secondary">\n                                        <i class="fas fa-redo me-1"></i> New Decryption\n                                    </button>\n                                </div>\n                            </div>\n                            \n                            <div id="error-result" class="d-none">\n                                <div class="alert alert-danger">\n                                    <h4 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Decryption Failed</h4>\n                                    <p id="error-message">Unable to decrypt the file with the current settings.</p>\n                                </div>\n                                <button id="try-again" class="btn btn-primary me-2">\n                                    <i class="fas fa-redo me-1"></i> Try Again\n                                </button>\n                                <button id="try-different" class="btn btn-outline-secondary">\n                                    <i class="fas fa-sync-alt me-1"></i> Try Different Method\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                \n                <div class="text-center text-muted mt-4">\n                    <p>Ultimate Decryption Suite v1.0.0 &copy; 2023</p>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>\n    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>\n    <script>\n        // Global variables\n        let currentFile = null;\n        let taskId = null;\n        let progressInterval = null;\n        \n        // DOM Elements\n        const fileInput = document.getElementById('file-input');\n        const dropZone = document.getElementById('drop-zone');\n        const fileInfo = document.getElementById('file-info');\n        const fileName = document.getElementById('file-name');\n        const fileSize = document.getElementById('file-size');\n        const changeFileBtn = document.getElementById('change-file');\n        const uploadSection = document.getElementById('upload-section');\n        const progressSection = document.getElementById('progress-section');\n        const resultSection = document.getElementById('result-section');\n        const successResult = document.getElementById('success-result');\n        const errorResult = document.getElementById('error-result');\n        const progressBar = document.getElementById('progress-bar');\n        const progressStatus = document.getElementById('progress-status');\n        const progressSpeed = document.getElementById('progress-speed');\n        const attemptsEl = document.getElementById('attempts');\n        const speedEl = document.getElementById('speed');\n        const fileList = document.getElementById('file-list');\n        const downloadAllBtn = document.getElementById('download-all');\n        const newDecryptBtn = document.getElementById('new-decrypt');\n        const tryAgainBtn = document.getElementById('try-again');\n        const tryDifferentBtn = document.getElementById('try-different');\n        const wordlistSelect = document.getElementById('wordlist');\n        const customWordlistInput = document.getElementById('custom-wordlist');\n        \n        // Event Listeners\n        dropZone.addEventListener('click', () => fileInput.click());\n        \n        fileInput.addEventListener('change', (e) => {\n            if (e.target.files.length > 0) {\n                handleFileSelect(e.target.files[0]);\n            }\n        });\n        \n        // Handle drag and drop\n        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {\n            dropZone.addEventListener(eventName, preventDefaults, false);\n        });\n        \n        function preventDefaults(e) {\n            e.preventDefault();\n            e.stopPropagation();\n        }\n        \n        ['dragenter', 'dragover'].forEach(eventName => {\n            dropZone.addEventListener(eventName, highlight, false);\n        });\n        \n        ['dragleave', 'drop'].forEach(eventName => {\n            dropZone.addEventListener(eventName, unhighlight, false);\n        });\n        \n        function highlight() {\n            dropZone.classList.add('bg-light');\n        }\n        \n        function unhighlight() {\n            dropZone.classList.remove('bg-light');\n        }\n        \n        dropZone.addEventListener('drop', handleDrop, false);\n        \n        function handleDrop(e) {\n            const dt = e.dataTransfer;\n            const file = dt.files[0];\n            handleFileSelect(file);\n        }\n        \n        changeFileBtn.addEventListener('click', () => {\n            fileInput.value = '';\n            currentFile = null;\n            fileInfo.classList.add('d-none');\n            dropZone.classList.remove('d-none');\n        });\n        \n        document.getElementById('start-decrypt').addEventListener('click', startDecryption);\n        document.getElementById('stop-decrypt').addEventListener('click', stopDecryption);\n        newDecryptBtn.addEventListener('click', resetUI);\n        tryAgainBtn.addEventListener('click', () => {\n            resultSection.classList.add('d-none');\n            startDecryption();\n        });\n        tryDifferentBtn.addEventListener('click', resetUI);\n        \n        wordlistSelect.addEventListener('change', (e) => {\n            if (e.target.value === 'custom') {\n                customWordlistInput.classList.remove('d-none');\n            } else {\n                customWordlistInput.classList.add('d-none');\n            }\n        });\n        \n        // Functions\n        function handleFileSelect(file) {\n            if (!file) return;\n            \n            currentFile = file;\n            fileName.textContent = file.name;\n            fileSize.textContent = formatFileSize(file.size);\n            \n            // Show file info and hide drop zone\n            fileInfo.classList.remove('d-none');\n            dropZone.classList.add('d-none');\n        }\n        \n        function formatFileSize(bytes) {\n            if (bytes === 0) return '0 Bytes';\n            const k = 1024;\n            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n            const i = Math.floor(Math.log(bytes) / Math.log(k));\n            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n        }\n        \n        async function startDecryption() {\n            if (!currentFile) {\n                alert('Please select a file first');\n                return;\n            }\n            \n            // Show progress section\n            uploadSection.classList.add('d-none');\n            progressSection.classList.remove('d-none');\n            resultSection.classList.add('d-none');\n            \n            // Prepare form data\n            const formData = new FormData();\n            formData.append('file', currentFile);\n            formData.append('method', document.getElementById('decryption-method').value);\n            formData.append('use_gpu', document.getElementById('use-gpu').checked);\n            formData.append('min_length', document.getElementById('min-length').value);\n            formData.append('max_length', document.getElementById('max-length').value);\n            \n            // Add wordlist if custom\n            if (wordlistSelect.value === 'custom' && customWordlistInput.files.length > 0) {\n                formData.append('wordlist', customWordlistInput.files[0]);\n            } else {\n                formData.append('wordlist_type', wordlistSelect.value);\n            }\n            \n            try {\n                // Start decryption\n                const response = await axios.post('/api/decrypt', formData, {\n                    headers: {\n                        'Content-Type': 'multipart/form-data'\n                    }\n                });\n                \n                taskId = response.data.task_id;\n                console.log('Decryption started with task ID:', taskId);\n                \n                // Start polling for progress\n                progressInterval = setInterval(pollProgress, 1000);\n                \n            } catch (error) {\n                console.error('Error starting decryption:', error);\n                showError('Failed to start decryption: ' + (error.response?.data?.detail || error.message));\n            }\n        }\n        \n        async function pollProgress() {\n            if (!taskId) return;\n            \n            try {\n                const response = await axios.get(`/api/progress/${taskId}`);\n                const data = response.data;\n                \n                // Update progress\n                const progress = data.progress || 0;\n                progressBar.style.width = `${progress}%`;\n                progressBar.textContent = `${Math.round(progress)}%`;\n                \n                // Update status\n                if (data.status) {\n                    progressStatus.textContent = data.status;\n                }\n                \n                // Update stats\n                if (data.attempts !== undefined) {\n                    attemptsEl.textContent = data.attempts.toLocaleString();\n                }\n                \n                if (data.speed !== undefined) {\n                    const speed = formatSpeed(data.speed);\n                    speedEl.textContent = speed;\n                    progressSpeed.textContent = `${speed}/s`;\n                }\n                \n                // Check if decryption is complete\n                if (data.state === 'completed') {\n                    clearInterval(progressInterval);\n                    showResult(data.result);\n                } else if (data.state === 'failed') {\n                    clearInterval(progressInterval);\n                    showError(data.error || 'Decryption failed');\n                }\n                \n            } catch (error) {\n                console.error('Error polling progress:', error);\n                clearInterval(progressInterval);\n                showError('Error checking decryption progress');\n            }\n        }\n        \n        function formatSpeed(speed) {\n            if (speed < 1000) return `${speed} hashes/s`;\n            if (speed < 1000000) return `${(speed / 1000).toFixed(1)}K hashes/s`;\n            return `${(speed / 1000000).toFixed(1)}M hashes/s`;\n        }\n        \n        async function stopDecryption() {\n            if (!taskId) return;\n            \n            try {\n                await axios.post(`/api/stop/${taskId}`);\n                clearInterval(progressInterval);\n                showError('Decryption stopped by user');\n            } catch (error) {\n                console.error('Error stopping decryption:', error);\n            }\n        }\n        \n        function showResult(result) {\n            progressSection.classList.add('d-none');\n            resultSection.classList.remove('d-none');\n            \n            if (result.success) {\n                successResult.classList.remove('d-none');\n                errorResult.classList.add('d-none');\n                \n                // Update success details\n                document.getElementById('decryption-time').textContent = (result.time_taken || 0).toFixed(2);\n                \n                if (result.password_found) {\n                    document.getElementById('found-password').textContent = result.password_found;\n                }\n                \n                // Show extracted files\n                if (result.files && result.files.length > 0) {\n                    fileList.innerHTML = '';\n                    result.files.forEach((file, index) => {\n                        const fileItem = document.createElement('a');\n                        fileItem.href = '#';\n                        fileItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center' + (index === 0 ? ' active' : '');\n                        fileItem.innerHTML = `\n                            <div>\n                                <i class="fas fa-file-alt me-2"></i>\n                                ${file.name}\n                            </div>\n                            <span class="badge bg-primary rounded-pill">${formatFileSize(file.size)}</span>\n                        `;\n                        fileItem.addEventListener('click', (e) => {\n                            e.preventDefault();\n                            // Show file preview or download\n                            window.open(`/api/download/${result.task_id}/${encodeURIComponent(file.name)}`, '_blank');\n                        });\n                        fileList.appendChild(fileItem);\n                    });\n                }\n                \n                // Handle download all\n                downloadAllBtn.onclick = () => {\n                    window.open(`/api/download/${result.task_id}/all`, '_blank');\n                };\n                \n            } else {\n                successResult.classList.add('d-none');\n                errorResult.classList.remove('d-none');\n                \n                if (result.error) {\n                    document.getElementById('error-message').textContent = result.error;\n                }\n            }\n        }\n        \n        function showError(message) {\n            progressSection.classList.add('d-none');\n            resultSection.classList.remove('d-none');\n            successResult.classList.add('d-none');\n            errorResult.classList.remove('d-none');\n            document.getElementById('error-message').textContent = message;\n        }\n        \n        function resetUI() {\n            // Reset file input\n            fileInput.value = '';\n            currentFile = null;\n            \n            // Reset UI state\n            fileInfo.classList.add('d-none');\n            dropZone.classList.remove('d-none');\n            uploadSection.classList.remove('d-none');\n            progressSection.classList.add('d-none');\n            resultSection.classList.add('d-none');\n            successResult.classList.add('d-none');\n            errorResult.classList.add('d-none');\n            \n            // Reset progress\n            progressBar.style.width = '0%';
            progressBar.textContent = '0%';
            progressStatus.textContent = 'Initializing...';
            progressSpeed.textContent = '';
            attemptsEl.textContent = '0';
            speedEl.textContent = '0';
            
            // Clear any active tasks
            if (taskId) {
                // Notify server to clean up
                axios.post(`/api/stop/${taskId}`).catch(console.error);
                taskId = null;
            }
            
            // Clear interval if still running
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
        }
    </script>
</body>
</html>
"""
