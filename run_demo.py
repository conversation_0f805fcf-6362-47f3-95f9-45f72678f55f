#!/usr/bin/env python3
"""
عرض توضيحي شامل لخوارزمية التشفير المتقدمة
Comprehensive demo for Advanced Quantum-Resistant Encryption Algorithm
"""

import os
import sys
import time
import json
from advanced_crypto import QuantumResistantCrypto

def print_header():
    """طباعة رأس البرنامج"""
    header = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                          🔐 خوارزمية التشفير المتقدمة                          ║
║                    Advanced Quantum-Resistant Encryption                     ║
║                                                                              ║
║  🛡️  تشفير متعدد الطبقات مع مقاومة الحوسبة الكمية                              ║
║  🚀 AES-256-GCM + RSA-4096 + XOR متعدد المستويات + HMAC-SHA3-512            ║
║  ✅ حماية قصوى للبيانات الحساسة والسرية                                       ║
║                                                                              ║
║  المطور: فريق التشفير المتقدم                                                 ║
║  الإصدار: 1.0.0                                                             ║
║  التاريخ: 2024                                                               ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(header)

def demo_basic_encryption():
    """عرض توضيحي للتشفير الأساسي"""
    print("\n" + "="*80)
    print("🔒 العرض التوضيحي الأول: التشفير الأساسي")
    print("="*80)
    
    crypto = QuantumResistantCrypto()
    
    # البيانات التجريبية
    secret_message = """
    🔐 رسالة سرية للغاية 🔐
    
    هذه رسالة تحتوي على معلومات حساسة جداً:
    
    📋 معلومات شخصية:
    - الاسم: أحمد محمد علي
    - رقم الهوية: 1234567890
    - رقم الهاتف: +966501234567
    - البريد الإلكتروني: <EMAIL>
    
    💳 معلومات مالية:
    - رقم البطاقة: 4532-1234-5678-9012
    - تاريخ الانتهاء: 12/25
    - رمز الأمان: 123
    - الرصيد: 50,000 ريال سعودي
    
    🏢 معلومات العمل:
    - الشركة: شركة التقنية المتقدمة
    - المنصب: مدير تقنية المعلومات
    - الراتب: 25,000 ريال شهرياً
    - كلمة مرور النظام: SuperSecret@2024!
    
    ⚠️ هذه المعلومات سرية للغاية ومحمية بأقوى تقنيات التشفير!
    """
    
    password = "كلمة_مرور_قوية_جداً_للحماية_القصوى_2024!@#$%"
    
    print(f"📝 الرسالة الأصلية ({len(secret_message)} حرف):")
    print("─" * 50)
    print(secret_message)
    print("─" * 50)
    
    print(f"\n🔑 كلمة المرور المستخدمة: {password}")
    print(f"📊 قوة كلمة المرور: {len(password)} حرف")
    
    # التشفير
    print("\n🔒 جاري تشفير الرسالة...")
    start_time = time.time()
    
    encrypted_package = crypto.encrypt_data(secret_message.encode('utf-8'), password)
    
    encryption_time = time.time() - start_time
    encrypted_size = len(json.dumps(encrypted_package))
    
    print(f"✅ تم التشفير بنجاح في {encryption_time:.4f} ثانية")
    print(f"📦 حجم البيانات المشفرة: {encrypted_size:,} بايت")
    print(f"🛡️ الخوارزمية المستخدمة: {encrypted_package['algorithm']}")
    print(f"🕐 الطابع الزمني: {time.ctime(encrypted_package['timestamp'])}")
    
    # عرض جزء من البيانات المشفرة
    print(f"\n📄 عينة من البيانات المشفرة:")
    print("─" * 50)
    sample_data = json.dumps(encrypted_package, indent=2, ensure_ascii=False)[:500] + "..."
    print(sample_data)
    print("─" * 50)
    
    # فك التشفير
    print("\n🔓 جاري فك تشفير الرسالة...")
    start_time = time.time()
    
    decrypted_data = crypto.decrypt_data(encrypted_package, password)
    
    decryption_time = time.time() - start_time
    
    print(f"✅ تم فك التشفير بنجاح في {decryption_time:.4f} ثانية")
    
    # التحقق من صحة العملية
    original_bytes = secret_message.encode('utf-8')
    if original_bytes == decrypted_data:
        print("✅ التحقق من السلامة: نجح! البيانات متطابقة تماماً")
        print(f"📝 الرسالة المسترجعة ({len(decrypted_data.decode('utf-8'))} حرف):")
        print("─" * 50)
        print(decrypted_data.decode('utf-8'))
        print("─" * 50)
    else:
        print("❌ التحقق من السلامة: فشل! البيانات غير متطابقة")
    
    # إحصائيات الأداء
    print(f"\n📊 إحصائيات الأداء:")
    print(f"   ⏱️ وقت التشفير: {encryption_time:.4f} ثانية")
    print(f"   ⏱️ وقت فك التشفير: {decryption_time:.4f} ثانية")
    print(f"   ⏰ الوقت الإجمالي: {encryption_time + decryption_time:.4f} ثانية")
    print(f"   📈 نسبة الضغط: {(encrypted_size / len(original_bytes)) * 100:.1f}%")

def demo_hybrid_encryption():
    """عرض توضيحي للتشفير الهجين"""
    print("\n" + "="*80)
    print("🔐 العرض التوضيحي الثاني: التشفير الهجين (AES + RSA)")
    print("="*80)
    
    crypto = QuantumResistantCrypto()
    
    # توليد مفاتيح RSA
    print("🔑 جاري توليد زوج مفاتيح RSA-4096...")
    start_time = time.time()
    
    private_key, public_key = crypto.generate_rsa_keypair()
    
    key_generation_time = time.time() - start_time
    
    print(f"✅ تم توليد المفاتيح في {key_generation_time:.4f} ثانية")
    print(f"🔒 حجم المفتاح الخاص: {len(private_key):,} بايت")
    print(f"🔓 حجم المفتاح العام: {len(public_key):,} بايت")
    
    # البيانات السرية
    top_secret_data = """
    🏛️ وثيقة حكومية سرية للغاية 🏛️
    مستوى السرية: أعلى درجات السرية
    
    📋 خطة الأمن القومي 2024-2030
    ═══════════════════════════════════════
    
    🎯 الأهداف الاستراتيجية:
    1. تعزيز الأمن السيبراني الوطني
    2. حماية البنية التحتية الحيوية
    3. تطوير قدرات الدفاع الإلكتروني
    4. بناء نظام إنذار مبكر متقدم
    
    💰 الميزانية المخصصة:
    - المرحلة الأولى: 500 مليون ريال
    - المرحلة الثانية: 750 مليون ريال
    - المرحلة الثالثة: 1 مليار ريال
    
    🔐 كلمات مرور الأنظمة الحساسة:
    - النظام الرئيسي: UltraSecure@2024#Main
    - نظام النسخ الاحتياطي: BackupSafe@2024#Secure
    - نظام المراقبة: Monitor@2024#Watch
    
    👥 قائمة المسؤولين المخولين:
    - الوزير: د. عبدالله الأحمد
    - نائب الوزير: م. فاطمة السالم
    - مدير الأمن: أ. محمد الخالد
    
    ⚠️ تحذير: هذه الوثيقة سرية للغاية ومحمية بموجب قانون أمن المعلومات
    أي إفشاء أو تسريب يعرض المخالف للمساءلة القانونية الصارمة
    
    📅 تاريخ الإنشاء: 30 يوليو 2024
    🔏 مستوى التشفير: حماية كمية متقدمة
    """
    
    print(f"\n📄 الوثيقة السرية ({len(top_secret_data)} حرف):")
    print("─" * 50)
    print(top_secret_data[:300] + "... [تم اقتطاع الباقي للأمان]")
    print("─" * 50)
    
    # التشفير الهجين
    print("\n🔒 جاري التشفير الهجين (AES-256-GCM + RSA-4096)...")
    start_time = time.time()
    
    encrypted_package = crypto.encrypt_data(
        top_secret_data.encode('utf-8'), 
        public_key_pem=public_key
    )
    
    encryption_time = time.time() - start_time
    
    print(f"✅ تم التشفير الهجين في {encryption_time:.4f} ثانية")
    print(f"🔐 يستخدم RSA: {encrypted_package['uses_rsa']}")
    print(f"🛡️ الخوارزمية: {encrypted_package['algorithm']}")
    print(f"📦 حجم البيانات المشفرة: {len(json.dumps(encrypted_package)):,} بايت")
    
    # فك التشفير
    print("\n🔓 جاري فك التشفير باستخدام المفتاح الخاص...")
    start_time = time.time()
    
    decrypted_data = crypto.decrypt_data(encrypted_package, private_key_pem=private_key)
    
    decryption_time = time.time() - start_time
    
    print(f"✅ تم فك التشفير في {decryption_time:.4f} ثانية")
    
    # التحقق من صحة العملية
    if top_secret_data.encode('utf-8') == decrypted_data:
        print("✅ التحقق من سلامة الوثيقة: نجح! الوثيقة سليمة وغير معدلة")
        print("🏛️ تم استرجاع الوثيقة الحكومية السرية بنجاح")
    else:
        print("❌ التحقق من سلامة الوثيقة: فشل!")
    
    print(f"\n📊 إحصائيات التشفير الهجين:")
    print(f"   🔑 وقت توليد المفاتيح: {key_generation_time:.4f} ثانية")
    print(f"   🔒 وقت التشفير: {encryption_time:.4f} ثانية")
    print(f"   🔓 وقت فك التشفير: {decryption_time:.4f} ثانية")
    print(f"   ⏰ الوقت الإجمالي: {key_generation_time + encryption_time + decryption_time:.4f} ثانية")

def demo_security_tests():
    """عرض توضيحي لاختبارات الأمان"""
    print("\n" + "="*80)
    print("🛡️ العرض التوضيحي الثالث: اختبارات الأمان والحماية")
    print("="*80)
    
    crypto = QuantumResistantCrypto()
    
    test_data = "بيانات تجريبية لاختبار الأمان والحماية من الهجمات".encode('utf-8')
    correct_password = "كلمة_مرور_صحيحة_123!@#"
    
    # التشفير
    print("🔒 جاري تشفير البيانات التجريبية...")
    encrypted_package = crypto.encrypt_data(test_data, correct_password)
    print("✅ تم التشفير بنجاح")
    
    # اختبار 1: كلمة مرور خاطئة
    print("\n🔍 الاختبار الأول: مقاومة كلمات المرور الخاطئة")
    print("─" * 60)
    
    wrong_passwords = [
        "كلمة_مرور_خاطئة",
        "password123",
        "",
        "123456789",
        "كلمة_مرور_صحيحة_123!@",  # قريبة لكن خاطئة
    ]
    
    for i, wrong_password in enumerate(wrong_passwords, 1):
        try:
            crypto.decrypt_data(encrypted_package, wrong_password)
            print(f"   ❌ الاختبار {i}: فشل في رفض كلمة المرور: '{wrong_password}'")
        except ValueError:
            print(f"   ✅ الاختبار {i}: تم رفض كلمة المرور الخاطئة: '{wrong_password}'")
    
    # اختبار 2: مقاومة التلاعب
    print("\n🔍 الاختبار الثاني: مقاومة التلاعب بالبيانات")
    print("─" * 60)
    
    tamper_tests = [
        ("تعديل النص المشفر", "ciphertext"),
        ("تعديل المتجه الأولي", "iv"),
        ("تعديل العلامة", "tag"),
        ("تعديل الملح", "salt"),
        ("تعديل hash السلامة", "integrity_hash"),
    ]
    
    for i, (test_name, field) in enumerate(tamper_tests, 1):
        tampered_package = encrypted_package.copy()
        original_value = tampered_package[field]
        
        # تعديل البيانات
        if len(original_value) > 1:
            tampered_package[field] = original_value[:-1] + ('X' if original_value[-1] != 'X' else 'Y')
        else:
            tampered_package[field] = 'X'
        
        try:
            crypto.decrypt_data(tampered_package, correct_password)
            print(f"   ❌ الاختبار {i}: فشل في اكتشاف {test_name}")
        except ValueError:
            print(f"   ✅ الاختبار {i}: تم اكتشاف {test_name} بنجاح")
    
    # اختبار 3: اختبار التفرد
    print("\n🔍 الاختبار الثالث: تفرد البيانات المشفرة")
    print("─" * 60)
    
    # تشفير نفس البيانات عدة مرات
    encrypted_results = []
    for i in range(5):
        encrypted = crypto.encrypt_data(test_data, correct_password)
        encrypted_results.append(encrypted['ciphertext'])
    
    # التحقق من التفرد
    unique_results = set(encrypted_results)
    if len(unique_results) == len(encrypted_results):
        print("   ✅ جميع النتائج فريدة - لا يوجد تكرار في البيانات المشفرة")
        print(f"   📊 تم إنتاج {len(unique_results)} نتيجة مختلفة من {len(encrypted_results)} محاولة")
    else:
        print("   ❌ وجد تكرار في البيانات المشفرة!")
    
    print("\n🏆 ملخص اختبارات الأمان:")
    print("   🛡️ مقاومة كلمات المرور الخاطئة: ممتازة")
    print("   🔒 مقاومة التلاعب بالبيانات: ممتازة") 
    print("   🎲 تفرد البيانات المشفرة: ممتاز")
    print("   ⭐ التقييم العام للأمان: ممتاز")

def demo_performance():
    """عرض توضيحي لقياس الأداء"""
    print("\n" + "="*80)
    print("⚡ العرض التوضيحي الرابع: قياس الأداء والسرعة")
    print("="*80)
    
    crypto = QuantumResistantCrypto()
    
    # أحجام مختلفة للاختبار
    test_sizes = [
        (1024, "1 KB"),
        (10 * 1024, "10 KB"),
        (100 * 1024, "100 KB"),
        (1024 * 1024, "1 MB"),
        (5 * 1024 * 1024, "5 MB"),
    ]
    
    print("📊 جاري قياس الأداء لأحجام مختلفة من البيانات...")
    print("─" * 80)
    
    results = []
    
    for size_bytes, size_label in test_sizes:
        print(f"\n🔍 اختبار حجم {size_label} ({size_bytes:,} بايت):")
        
        try:
            benchmark_result = crypto.benchmark_encryption(size_bytes)
            results.append((size_label, benchmark_result))
            
            print(f"   ⏱️ وقت التشفير: {benchmark_result['encryption_time_seconds']:.4f} ثانية")
            print(f"   ⏱️ وقت فك التشفير: {benchmark_result['decryption_time_seconds']:.4f} ثانية")
            print(f"   🚀 سرعة التشفير: {benchmark_result['encryption_speed_mbps']:.2f} MB/s")
            print(f"   🚀 سرعة فك التشفير: {benchmark_result['decryption_speed_mbps']:.2f} MB/s")
            print(f"   ⏰ الوقت الإجمالي: {benchmark_result['total_time_seconds']:.4f} ثانية")
            
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")
    
    # ملخص النتائج
    print("\n" + "="*80)
    print("📈 ملخص نتائج قياس الأداء")
    print("="*80)
    
    print(f"{'الحجم':<10} {'تشفير (s)':<12} {'فك تشفير (s)':<15} {'سرعة تشفير':<15} {'سرعة فك تشفير':<18}")
    print("─" * 80)
    
    for size_label, result in results:
        print(f"{size_label:<10} {result['encryption_time_seconds']:<12.4f} "
              f"{result['decryption_time_seconds']:<15.4f} "
              f"{result['encryption_speed_mbps']:<15.2f} "
              f"{result['decryption_speed_mbps']:<18.2f}")
    
    if results:
        avg_enc_speed = sum(r[1]['encryption_speed_mbps'] for r in results) / len(results)
        avg_dec_speed = sum(r[1]['decryption_speed_mbps'] for r in results) / len(results)
        
        print("─" * 80)
        print(f"{'المتوسط':<10} {'─':<12} {'─':<15} {avg_enc_speed:<15.2f} {avg_dec_speed:<18.2f}")
        
        print(f"\n🏆 تقييم الأداء:")
        if avg_enc_speed > 10:
            print("   ⭐⭐⭐ سرعة التشفير: ممتازة")
        elif avg_enc_speed > 5:
            print("   ⭐⭐ سرعة التشفير: جيدة")
        else:
            print("   ⭐ سرعة التشفير: مقبولة")
        
        if avg_dec_speed > 10:
            print("   ⭐⭐⭐ سرعة فك التشفير: ممتازة")
        elif avg_dec_speed > 5:
            print("   ⭐⭐ سرعة فك التشفير: جيدة")
        else:
            print("   ⭐ سرعة فك التشفير: مقبولة")

def main():
    """الدالة الرئيسية للعرض التوضيحي"""
    print_header()
    
    print("\n🚀 مرحباً بك في العرض التوضيحي الشامل لخوارزمية التشفير المتقدمة!")
    print("هذا العرض سيوضح لك جميع إمكانيات وميزات النظام المتطورة.")
    
    input("\n📱 اضغط Enter للمتابعة...")
    
    try:
        # العروض التوضيحية
        demo_basic_encryption()
        input("\n📱 اضغط Enter للانتقال للعرض التالي...")
        
        demo_hybrid_encryption()
        input("\n📱 اضغط Enter للانتقال للعرض التالي...")
        
        demo_security_tests()
        input("\n📱 اضغط Enter للانتقال للعرض التالي...")
        
        demo_performance()
        
        # الخاتمة
        print("\n" + "="*80)
        print("🎉 انتهى العرض التوضيحي الشامل!")
        print("="*80)
        
        print("""
✅ تم عرض جميع ميزات خوارزمية التشفير المتقدمة بنجاح:

🔐 التشفير الأساسي متعدد الطبقات
🔑 التشفير الهجين (AES + RSA)
🛡️ اختبارات الأمان والحماية
⚡ قياس الأداء والسرعة

🏆 النتيجة: خوارزمية التشفير جاهزة للاستخدام الإنتاجي!

📚 للمزيد من المعلومات:
   - اقرأ ملف README.md
   - شغل python crypto_examples.py
   - استخدم python crypto_cli.py --help
   - جرب تطبيق الويب: python crypto_web_app.py

🔐 حماية بياناتك أولوية قصوى!
        """)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف العرض التوضيحي بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في العرض التوضيحي: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
