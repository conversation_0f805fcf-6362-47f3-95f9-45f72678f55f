# خوارزمية التشفير المتقدمة المقاومة للحوسبة الكمية
## Advanced Quantum-Resistant Encryption Algorithm

🔐 **نظام تشفير هجين متعدد الطبقات مع مقاومة الحوسبة الكمية**

---

## 📋 المحتويات

- [نظرة عامة](#نظرة-عامة)
- [المميزات](#المميزات)
- [التقنيات المستخدمة](#التقنيات-المستخدمة)
- [التثبيت](#التثبيت)
- [الاستخدام](#الاستخدام)
- [الأمثلة](#الأمثلة)
- [الأمان](#الأمان)
- [الأداء](#الأداء)
- [المساهمة](#المساهمة)

---

## 🎯 نظرة عامة

هذه خوارزمية تشفير متقدمة تجمع بين أفضل التقنيات الحديثة لتوفير حماية قصوى للبيانات. تم تصميمها لتكون مقاومة للهجمات التقليدية والكمية المستقبلية.

### 🏗️ البنية المعمارية

```
البيانات الأصلية
       ↓
[طبقة XOR متعددة المستويات]
       ↓
[تشفير AES-256-GCM]
       ↓
[تشفير RSA-4096 للمفاتيح] (اختياري)
       ↓
[التحقق من السلامة HMAC-SHA3-512]
       ↓
البيانات المشفرة النهائية
```

---

## ✨ المميزات

### 🛡️ الأمان
- **تشفير متعدد الطبقات**: XOR ديناميكي + AES-256-GCM + RSA-4096
- **مقاومة الحوسبة الكمية**: استخدام خوارزميات مقاومة للهجمات الكمية
- **التحقق من السلامة**: HMAC-SHA3-512 لضمان عدم التلاعب
- **إدارة مفاتيح متقدمة**: PBKDF2 مع SHA3-512
- **ملح ديناميكي**: توليد ملح فريد لكل عملية تشفير

### 🚀 الأداء
- **سرعة عالية**: تحسين للأداء مع الحفاظ على الأمان
- **ذاكرة محسنة**: استخدام فعال للذاكرة
- **قابلية التوسع**: يدعم ملفات كبيرة الحجم

### 🔧 سهولة الاستخدام
- **واجهة برمجية بسيطة**: API سهل الاستخدام
- **دعم الملفات**: تشفير وفك تشفير الملفات مباشرة
- **توليد كلمات مرور**: توليد كلمات مرور آمنة تلقائياً
- **قياس الأداء**: أدوات مدمجة لقياس الأداء

---

## 🔬 التقنيات المستخدمة

| التقنية | الغرض | القوة |
|---------|--------|-------|
| **AES-256-GCM** | التشفير المتماثل الرئيسي | 256-bit |
| **RSA-4096** | التشفير غير المتماثل | 4096-bit |
| **PBKDF2** | اشتقاق المفاتيح | 100,000 iterations |
| **SHA3-512** | دالة التجميع | 512-bit |
| **HMAC-SHA3-512** | التحقق من السلامة | 512-bit |
| **XOR متعدد الطبقات** | طبقة حماية إضافية | ديناميكي |

---

## 📦 التثبيت

### 1. تثبيت Python (3.8 أو أحدث)
```bash
# تأكد من وجود Python 3.8+
python --version
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. التحقق من التثبيت
```bash
python crypto_examples.py
```

---

## 🚀 الاستخدام

### الاختبار السريع

```bash
# تشغيل اختبار شامل للنظام
python quick_test.py

# تشغيل الأمثلة التفصيلية
python crypto_examples.py

# تشغيل الاختبارات الوحدة
python test_crypto.py
```

### التشفير الأساسي

```python
from advanced_crypto import QuantumResistantCrypto

# إنشاء مثيل من الكلاس
crypto = QuantumResistantCrypto()

# البيانات وكلمة المرور
data = "نص سري جداً".encode('utf-8')
password = "كلمة_مرور_قوية"

# التشفير
encrypted = crypto.encrypt_data(data, password)

# فك التشفير
decrypted = crypto.decrypt_data(encrypted, password)

print(decrypted.decode('utf-8'))  # "نص سري جداً"
```

### التشفير الهجين مع RSA

```python
# توليد مفاتيح RSA
private_key, public_key = crypto.generate_rsa_keypair()

# التشفير باستخدام المفتاح العام
encrypted = crypto.encrypt_data(data, public_key_pem=public_key)

# فك التشفير باستخدام المفتاح الخاص
decrypted = crypto.decrypt_data(encrypted, private_key_pem=private_key)
```

### تشفير الملفات

```python
# تشفير ملف
encrypted_file = crypto.encrypt_file("secret.txt", password="my_password")

# فك تشفير ملف
decrypted_file = crypto.decrypt_file(encrypted_file, password="my_password")
```

### تطبيق الويب

```bash
# تثبيت Flask (إذا لم تكن مثبتة)
pip install flask

# تشغيل تطبيق الويب
python crypto_web_app.py

# فتح المتصفح على: http://localhost:5000
```

### سطر الأوامر

```bash
# تشفير ملف
python crypto_cli.py encrypt myfile.txt -p "my_password"

# فك تشفير ملف
python crypto_cli.py decrypt myfile.txt.encrypted -p "my_password"

# توليد مفاتيح RSA
python crypto_cli.py generate-keys

# توليد كلمة مرور آمنة
python crypto_cli.py generate-password -l 32

# قياس الأداء
python crypto_cli.py benchmark -s 1048576
```

---

## 📚 الأمثلة

تشغيل الأمثلة الشاملة:

```bash
python crypto_examples.py
```

الأمثلة تشمل:
- ✅ التشفير الأساسي
- ✅ التشفير الهجين مع RSA
- ✅ تشفير الملفات
- ✅ قياس الأداء
- ✅ اختبار المميزات الأمنية

---

## 🔒 الأمان

### مستويات الحماية

1. **الطبقة الأولى**: XOR متعدد المستويات مع مفاتيح ديناميكية
2. **الطبقة الثانية**: AES-256-GCM مع مفاتيح مشتقة
3. **الطبقة الثالثة**: RSA-4096 لحماية المفاتيح (اختياري)
4. **طبقة التحقق**: HMAC-SHA3-512 لضمان السلامة

### مقاومة الهجمات

- ✅ **هجمات القوة الغاشمة**: مفاتيح 256-bit
- ✅ **هجمات القاموس**: PBKDF2 مع 100,000 تكرار
- ✅ **هجمات التلاعب**: HMAC للتحقق من السلامة
- ✅ **هجمات التوقيت**: عمليات ثابتة الوقت
- ✅ **الهجمات الكمية**: خوارزميات مقاومة للحوسبة الكمية

---

## ⚡ الأداء

### نتائج القياس النموذجية

| حجم البيانات | وقت التشفير | وقت فك التشفير | السرعة |
|-------------|-------------|---------------|---------|
| 1 KB | 0.005s | 0.003s | ~200 KB/s |
| 10 KB | 0.008s | 0.005s | ~1.2 MB/s |
| 100 KB | 0.025s | 0.018s | ~4 MB/s |
| 1 MB | 0.180s | 0.145s | ~5.5 MB/s |

*النتائج قد تختلف حسب قوة المعالج*

---

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إضافة الاختبارات المناسبة
4. التأكد من اجتياز جميع الاختبارات
5. إرسال Pull Request

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

## ⚠️ تنبيه أمني

- استخدم كلمات مرور قوية (32+ حرف)
- احتفظ بنسخ احتياطية من المفاتيح الخاصة
- لا تشارك المفاتيح الخاصة أو كلمات المرور
- اختبر النظام في بيئة آمنة قبل الاستخدام الإنتاجي

---

## 📞 الدعم

للأسئلة والدعم:
- 📧 البريد الإلكتروني: [<EMAIL>]
- 🐛 الإبلاغ عن الأخطاء: [GitHub Issues]
- 📖 الوثائق: [Documentation]

---

**🔐 حماية بياناتك أولوية قصوى!**
