#!/usr/bin/env python3
"""
إعداد حزمة خوارزمية التشفير المتقدمة
Setup script for Advanced Quantum-Resistant Encryption Algorithm
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# قراءة متطلبات المشروع
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="quantum-resistant-crypto",
    version="1.0.0",
    author="Advanced Crypto Team",
    author_email="<EMAIL>",
    description="خوارزمية تشفير متقدمة مقاومة للحوسبة الكمية",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/example/quantum-resistant-crypto",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Intended Audience :: Information Technology",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Security :: Cryptography",
        "Topic :: Security",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "web": ["flask>=2.0.0"],
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.0.0",
            "black>=21.0.0",
            "flake8>=3.8.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "quantum-crypto=crypto_cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.html"],
    },
    keywords=[
        "encryption", "cryptography", "quantum-resistant", "security",
        "aes", "rsa", "hybrid-encryption", "multi-layer",
        "تشفير", "أمان", "حماية", "كمي"
    ],
    project_urls={
        "Bug Reports": "https://github.com/example/quantum-resistant-crypto/issues",
        "Source": "https://github.com/example/quantum-resistant-crypto",
        "Documentation": "https://github.com/example/quantum-resistant-crypto/wiki",
    },
)
