#!/usr/bin/env python3
"""
اختبار سريع لخوارزمية التشفير المتقدمة
Quick test for Advanced Quantum-Resistant Encryption Algorithm
"""

from advanced_crypto import QuantumResistantCrypto
import time
import json

def test_basic_encryption():
    """اختبار التشفير الأساسي"""
    print("🔒 اختبار التشفير الأساسي...")
    
    crypto = QuantumResistantCrypto()
    
    # البيانات التجريبية
    test_data = "مرحباً بالعالم! هذا اختبار للتشفير المتقدم 🔐".encode('utf-8')
    password = "كلمة_مرور_قوية_123!@#"
    
    print(f"📝 النص الأصلي: {test_data.decode('utf-8')}")
    print(f"🔑 كلمة المرور: {password}")
    
    # التشفير
    start_time = time.time()
    encrypted = crypto.encrypt_data(test_data, password)
    encryption_time = time.time() - start_time
    
    print(f"✅ تم التشفير في {encryption_time:.4f} ثانية")
    print(f"📊 حجم البيانات المشفرة: {len(json.dumps(encrypted))} بايت")
    
    # فك التشفير
    start_time = time.time()
    decrypted = crypto.decrypt_data(encrypted, password)
    decryption_time = time.time() - start_time
    
    print(f"✅ تم فك التشفير في {decryption_time:.4f} ثانية")
    print(f"📝 النص المفكوك: {decrypted.decode('utf-8')}")
    
    # التحقق
    if test_data == decrypted:
        print("✅ الاختبار نجح! البيانات متطابقة")
        return True
    else:
        print("❌ الاختبار فشل! البيانات غير متطابقة")
        return False

def test_rsa_encryption():
    """اختبار التشفير الهجين مع RSA"""
    print("\n🔐 اختبار التشفير الهجين (RSA + AES)...")
    
    crypto = QuantumResistantCrypto()
    
    # توليد مفاتيح RSA
    print("🔑 توليد مفاتيح RSA-4096...")
    start_time = time.time()
    private_key, public_key = crypto.generate_rsa_keypair()
    key_time = time.time() - start_time
    print(f"✅ تم توليد المفاتيح في {key_time:.4f} ثانية")
    
    # البيانات التجريبية
    test_data = """
    بيانات سرية للغاية تحتاج لحماية قصوى:
    - معلومات شخصية حساسة
    - بيانات مالية سرية  
    - كلمات مرور وأرقام سرية
    - وثائق حكومية مصنفة
    
    هذه البيانات محمية بتشفير هجين متقدم! 🛡️
    """.encode('utf-8')
    
    print(f"📝 حجم البيانات: {len(test_data)} بايت")
    
    # التشفير الهجين
    start_time = time.time()
    encrypted = crypto.encrypt_data(test_data, public_key_pem=public_key)
    encryption_time = time.time() - start_time
    
    print(f"✅ تم التشفير الهجين في {encryption_time:.4f} ثانية")
    print(f"🔐 يستخدم RSA: {encrypted['uses_rsa']}")
    print(f"🛡️ الخوارزمية: {encrypted['algorithm']}")
    
    # فك التشفير
    start_time = time.time()
    decrypted = crypto.decrypt_data(encrypted, private_key_pem=private_key)
    decryption_time = time.time() - start_time
    
    print(f"✅ تم فك التشفير في {decryption_time:.4f} ثانية")
    
    # التحقق
    if test_data == decrypted:
        print("✅ اختبار التشفير الهجين نجح!")
        return True
    else:
        print("❌ اختبار التشفير الهجين فشل!")
        return False

def test_file_encryption():
    """اختبار تشفير الملفات"""
    print("\n📁 اختبار تشفير الملفات...")
    
    crypto = QuantumResistantCrypto()
    
    # إنشاء ملف تجريبي
    test_file = "test_document.txt"
    test_content = """
    وثيقة سرية - مستوى السرية: عالي جداً
    ==========================================
    
    هذه وثيقة تحتوي على معلومات حساسة:
    
    1. خطط استراتيجية للشركة
    2. قوائم العملاء والموردين
    3. البيانات المالية السرية
    4. معلومات الموظفين الشخصية
    5. كلمات مرور الأنظمة الحساسة
    
    تاريخ الإنشاء: {date}
    مستوى الحماية: تشفير متقدم مقاوم للحوسبة الكمية
    
    ⚠️ تحذير: هذه الوثيقة سرية للغاية!
    """.replace("{date}", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # كتابة الملف
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"📄 تم إنشاء الملف: {test_file}")
    
    # تشفير الملف
    password = crypto.generate_secure_password(32)
    print(f"🔑 كلمة مرور مولدة: {password}")
    
    start_time = time.time()
    encrypted_file = crypto.encrypt_file(test_file, password=password)
    encryption_time = time.time() - start_time
    
    print(f"✅ تم تشفير الملف في {encryption_time:.4f} ثانية")
    print(f"🔐 الملف المشفر: {encrypted_file}")
    
    # فك تشفير الملف
    start_time = time.time()
    decrypted_file = crypto.decrypt_file(encrypted_file, password=password)
    decryption_time = time.time() - start_time
    
    print(f"✅ تم فك تشفير الملف في {decryption_time:.4f} ثانية")
    print(f"📄 الملف المفكوك: {decrypted_file}")
    
    # مقارنة المحتوى
    with open(test_file, 'r', encoding='utf-8') as f:
        original = f.read()
    
    with open(decrypted_file, 'r', encoding='utf-8') as f:
        decrypted_content = f.read()
    
    # تنظيف الملفات
    import os
    for file in [test_file, encrypted_file, decrypted_file]:
        if os.path.exists(file):
            os.remove(file)
    
    if original == decrypted_content:
        print("✅ اختبار تشفير الملفات نجح!")
        return True
    else:
        print("❌ اختبار تشفير الملفات فشل!")
        return False

def test_performance():
    """اختبار الأداء"""
    print("\n⚡ اختبار الأداء...")
    
    crypto = QuantumResistantCrypto()
    
    # اختبار أحجام مختلفة
    test_sizes = [1024, 10*1024, 100*1024]  # 1KB, 10KB, 100KB
    
    for size in test_sizes:
        print(f"\n📊 اختبار حجم {size/1024:.0f} KB:")
        
        try:
            results = crypto.benchmark_encryption(size)
            
            print(f"   ⏱️ وقت التشفير: {results['encryption_time_seconds']:.4f}s")
            print(f"   ⏱️ وقت فك التشفير: {results['decryption_time_seconds']:.4f}s")
            print(f"   🚀 سرعة التشفير: {results['encryption_speed_mbps']:.2f} MB/s")
            print(f"   🚀 سرعة فك التشفير: {results['decryption_speed_mbps']:.2f} MB/s")
            
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")
            return False
    
    print("✅ اختبار الأداء مكتمل!")
    return True

def test_security_features():
    """اختبار المميزات الأمنية"""
    print("\n🛡️ اختبار المميزات الأمنية...")
    
    crypto = QuantumResistantCrypto()
    test_data = "بيانات تجريبية للاختبار الأمني".encode('utf-8')
    password = "test_password_123"
    
    # التشفير
    encrypted = crypto.encrypt_data(test_data, password)
    
    # اختبار 1: كلمة مرور خاطئة
    print("🔍 اختبار كلمة مرور خاطئة...")
    try:
        crypto.decrypt_data(encrypted, "wrong_password")
        print("❌ فشل في رفض كلمة المرور الخاطئة!")
        return False
    except ValueError:
        print("✅ تم رفض كلمة المرور الخاطئة بنجاح")
    
    # اختبار 2: تعديل البيانات المشفرة
    print("🔍 اختبار مقاومة التلاعب...")
    tampered = encrypted.copy()
    tampered['ciphertext'] = tampered['ciphertext'][:-1] + 'X'
    
    try:
        crypto.decrypt_data(tampered, password)
        print("❌ فشل في اكتشاف التلاعب!")
        return False
    except ValueError:
        print("✅ تم اكتشاف التلاعب بنجاح")
    
    # اختبار 3: تعديل hash السلامة
    print("🔍 اختبار hash السلامة...")
    tampered_hash = encrypted.copy()
    tampered_hash['integrity_hash'] = tampered_hash['integrity_hash'][:-1] + 'Y'
    
    try:
        crypto.decrypt_data(tampered_hash, password)
        print("❌ فشل في اكتشاف تعديل hash السلامة!")
        return False
    except ValueError:
        print("✅ تم اكتشاف تعديل hash السلامة بنجاح")
    
    print("✅ جميع اختبارات الأمان نجحت!")
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار السريع لخوارزمية التشفير المتقدمة")
    print("=" * 60)
    
    tests = [
        ("التشفير الأساسي", test_basic_encryption),
        ("التشفير الهجين RSA", test_rsa_encryption),
        ("تشفير الملفات", test_file_encryption),
        ("الأداء", test_performance),
        ("المميزات الأمنية", test_security_features)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! خوارزمية التشفير جاهزة للاستخدام")
        print("🔐 النظام آمن وموثوق")
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
