#!/usr/bin/env python3
"""
تطبيق ويب لخوارزمية التشفير المتقدمة
Web Application for Advanced Quantum-Resistant Encryption Algorithm
"""

try:
    from flask import Flask, render_template, request, jsonify, send_file
except ImportError:
    print("❌ مكتبة Flask غير مثبتة!")
    print("💡 لتثبيتها، استخدم الأمر: pip install flask")
    exit(1)
import os
import json
import base64
import tempfile
from advanced_crypto import QuantumResistantCrypto
import time

app = Flask(__name__)
app.secret_key = 'quantum_resistant_crypto_secret_key_2024'

# إنشاء مثيل من خوارزمية التشفير
crypto = QuantumResistantCrypto()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/api/encrypt', methods=['POST'])
def api_encrypt():
    """API لتشفير البيانات"""
    try:
        data = request.json
        
        # استخراج البيانات
        text = data.get('text', '')
        password = data.get('password', '')
        use_rsa = data.get('use_rsa', False)
        
        if not text or not password:
            return jsonify({
                'success': False,
                'error': 'يجب إدخال النص وكلمة المرور'
            })
        
        # تحويل النص إلى bytes
        text_bytes = text.encode('utf-8')
        
        # التشفير
        start_time = time.time()
        
        if use_rsa:
            # توليد مفاتيح RSA للتشفير الهجين
            private_key, public_key = crypto.generate_rsa_keypair()
            encrypted_package = crypto.encrypt_data(text_bytes, public_key_pem=public_key)
            
            # إضافة المفتاح الخاص للاستجابة (في التطبيق الحقيقي يجب حفظه بشكل آمن)
            encrypted_package['private_key_pem'] = base64.b64encode(private_key).decode()
        else:
            encrypted_package = crypto.encrypt_data(text_bytes, password)
        
        encryption_time = time.time() - start_time
        
        return jsonify({
            'success': True,
            'encrypted_data': encrypted_package,
            'encryption_time': round(encryption_time, 4),
            'data_size': len(json.dumps(encrypted_package)),
            'algorithm': encrypted_package['algorithm']
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في التشفير: {str(e)}'
        })

@app.route('/api/decrypt', methods=['POST'])
def api_decrypt():
    """API لفك تشفير البيانات"""
    try:
        data = request.json
        
        # استخراج البيانات
        encrypted_package = data.get('encrypted_data')
        password = data.get('password', '')
        
        if not encrypted_package:
            return jsonify({
                'success': False,
                'error': 'يجب إدخال البيانات المشفرة'
            })
        
        # فك التشفير
        start_time = time.time()
        
        if encrypted_package.get('uses_rsa', False):
            # فك التشفير الهجين
            if 'private_key_pem' not in encrypted_package:
                return jsonify({
                    'success': False,
                    'error': 'المفتاح الخاص مطلوب لفك التشفير الهجين'
                })
            
            private_key = base64.b64decode(encrypted_package['private_key_pem'])
            decrypted_bytes = crypto.decrypt_data(encrypted_package, private_key_pem=private_key)
        else:
            if not password:
                return jsonify({
                    'success': False,
                    'error': 'كلمة المرور مطلوبة'
                })
            
            decrypted_bytes = crypto.decrypt_data(encrypted_package, password)
        
        decryption_time = time.time() - start_time
        
        # تحويل البيانات إلى نص
        decrypted_text = decrypted_bytes.decode('utf-8')
        
        return jsonify({
            'success': True,
            'decrypted_text': decrypted_text,
            'decryption_time': round(decryption_time, 4),
            'data_size': len(decrypted_bytes)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في فك التشفير: {str(e)}'
        })

@app.route('/api/generate-password')
def api_generate_password():
    """API لتوليد كلمة مرور آمنة"""
    try:
        length = request.args.get('length', 32, type=int)
        password = crypto.generate_secure_password(length)
        
        return jsonify({
            'success': True,
            'password': password,
            'length': len(password)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في توليد كلمة المرور: {str(e)}'
        })

@app.route('/api/benchmark')
def api_benchmark():
    """API لقياس أداء التشفير"""
    try:
        size = request.args.get('size', 1024, type=int)
        results = crypto.benchmark_encryption(size)
        
        return jsonify({
            'success': True,
            'benchmark_results': results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في قياس الأداء: {str(e)}'
        })

@app.route('/api/encrypt-file', methods=['POST'])
def api_encrypt_file():
    """API لتشفير الملفات"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'لم يتم اختيار ملف'
            })
        
        file = request.files['file']
        password = request.form.get('password', '')
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'لم يتم اختيار ملف'
            })
        
        if not password:
            return jsonify({
                'success': False,
                'error': 'كلمة المرور مطلوبة'
            })
        
        # قراءة محتوى الملف
        file_data = file.read()
        
        # التشفير
        encrypted_package = crypto.encrypt_data(file_data, password)
        
        # إنشاء ملف مؤقت للتحميل
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.encrypted')
        with open(temp_file.name, 'w', encoding='utf-8') as f:
            json.dump(encrypted_package, f, indent=2, ensure_ascii=False)
        
        return send_file(
            temp_file.name,
            as_attachment=True,
            download_name=f"{file.filename}.encrypted"
        )
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في تشفير الملف: {str(e)}'
        })

@app.route('/api/decrypt-file', methods=['POST'])
def api_decrypt_file():
    """API لفك تشفير الملفات"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'لم يتم اختيار ملف مشفر'
            })
        
        file = request.files['file']
        password = request.form.get('password', '')
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'لم يتم اختيار ملف'
            })
        
        if not password:
            return jsonify({
                'success': False,
                'error': 'كلمة المرور مطلوبة'
            })
        
        # قراءة الملف المشفر
        encrypted_data = json.loads(file.read().decode('utf-8'))
        
        # فك التشفير
        decrypted_data = crypto.decrypt_data(encrypted_data, password)
        
        # إنشاء ملف مؤقت للتحميل
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        with open(temp_file.name, 'wb') as f:
            f.write(decrypted_data)
        
        # تحديد اسم الملف
        original_filename = file.filename.replace('.encrypted', '')
        if original_filename == file.filename:
            original_filename = f"{file.filename}.decrypted"
        
        return send_file(
            temp_file.name,
            as_attachment=True,
            download_name=original_filename
        )
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في فك تشفير الملف: {str(e)}'
        })

if __name__ == '__main__':
    # إنشاء مجلد القوالب إذا لم يكن موجوداً
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("🚀 تشغيل تطبيق الويب لخوارزمية التشفير المتقدمة")
    print("🌐 الرابط: http://localhost:5000")
    print("🔐 جاهز لتشفير وفك تشفير البيانات!")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
