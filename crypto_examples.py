#!/usr/bin/env python3
"""
أمثلة على استخدام خوارزمية التشفير المتقدمة
Examples for Advanced Quantum-Resistant Encryption Algorithm
"""

from advanced_crypto import QuantumResistantCrypto
import json
import time


def example_basic_encryption():
    """مثال أساسي على التشفير وفك التشفير"""
    print("=" * 60)
    print("مثال أساسي على التشفير وفك التشفير")
    print("=" * 60)
    
    # إنشاء مثيل من الكلاس
    crypto = QuantumResistantCrypto()
    
    # البيانات المراد تشفيرها
    original_data = "هذا نص سري جداً يحتاج إلى تشفير قوي! 🔐".encode('utf-8')
    password = "كلمة_مرور_قوية_جداً_123!@#"
    
    print(f"البيانات الأصلية: {original_data.decode('utf-8')}")
    print(f"كلمة المرور: {password}")
    
    # التشفير
    print("\n🔒 جاري التشفير...")
    start_time = time.time()
    encrypted_package = crypto.encrypt_data(original_data, password)
    encryption_time = time.time() - start_time
    
    print(f"✅ تم التشفير بنجاح في {encryption_time:.4f} ثانية")
    print(f"حجم البيانات المشفرة: {len(json.dumps(encrypted_package))} بايت")
    
    # فك التشفير
    print("\n🔓 جاري فك التشفير...")
    start_time = time.time()
    decrypted_data = crypto.decrypt_data(encrypted_package, password)
    decryption_time = time.time() - start_time
    
    print(f"✅ تم فك التشفير بنجاح في {decryption_time:.4f} ثانية")
    print(f"البيانات بعد فك التشفير: {decrypted_data.decode('utf-8')}")
    
    # التحقق من صحة العملية
    if original_data == decrypted_data:
        print("✅ التحقق من صحة العملية: نجح!")
    else:
        print("❌ التحقق من صحة العملية: فشل!")


def example_rsa_hybrid_encryption():
    """مثال على التشفير الهجين مع RSA"""
    print("\n" + "=" * 60)
    print("مثال على التشفير الهجين مع RSA-4096")
    print("=" * 60)
    
    crypto = QuantumResistantCrypto()
    
    # توليد زوج مفاتيح RSA
    print("🔑 جاري توليد زوج مفاتيح RSA-4096...")
    start_time = time.time()
    private_key, public_key = crypto.generate_rsa_keypair()
    key_generation_time = time.time() - start_time
    print(f"✅ تم توليد المفاتيح في {key_generation_time:.4f} ثانية")
    
    # البيانات المراد تشفيرها
    secret_message = """
    هذه رسالة سرية للغاية تحتوي على معلومات حساسة:
    - أرقام بطاقات ائتمان: 1234-5678-9012-3456
    - كلمات مرور: SuperSecret123!@#
    - معلومات شخصية: محمد أحمد، 01234567890
    - بيانات مالية: حساب رقم 987654321
    
    هذه البيانات محمية بتشفير متقدم مقاوم للحوسبة الكمية! 🛡️
    """.encode('utf-8')
    
    print(f"حجم البيانات الأصلية: {len(secret_message)} بايت")
    
    # التشفير الهجين
    print("\n🔒 جاري التشفير الهجين (AES + RSA)...")
    start_time = time.time()
    encrypted_package = crypto.encrypt_data(secret_message, public_key_pem=public_key)
    encryption_time = time.time() - start_time
    
    print(f"✅ تم التشفير الهجين في {encryption_time:.4f} ثانية")
    print(f"الخوارزمية المستخدمة: {encrypted_package['algorithm']}")
    print(f"يستخدم RSA: {encrypted_package['uses_rsa']}")
    
    # فك التشفير
    print("\n🔓 جاري فك التشفير الهجين...")
    start_time = time.time()
    decrypted_message = crypto.decrypt_data(encrypted_package, private_key_pem=private_key)
    decryption_time = time.time() - start_time
    
    print(f"✅ تم فك التشفير في {decryption_time:.4f} ثانية")
    
    # التحقق من صحة العملية
    if secret_message == decrypted_message:
        print("✅ التحقق من صحة التشفير الهجين: نجح!")
        print("📄 البيانات المسترجعة:")
        print(decrypted_message.decode('utf-8'))
    else:
        print("❌ التحقق من صحة التشفير الهجين: فشل!")


def example_file_encryption():
    """مثال على تشفير الملفات"""
    print("\n" + "=" * 60)
    print("مثال على تشفير وفك تشفير الملفات")
    print("=" * 60)
    
    crypto = QuantumResistantCrypto()
    
    # إنشاء ملف تجريبي
    test_file = "test_secret_file.txt"
    secret_content = """
    ملف سري يحتوي على معلومات حساسة
    ==========================================
    
    معلومات الشركة السرية:
    - خطة التطوير للعام القادم
    - قائمة العملاء المهمين
    - البيانات المالية السرية
    - كلمات المرور للأنظمة الحساسة
    
    هذا الملف محمي بتشفير متقدم! 🔐
    
    تاريخ الإنشاء: {time}
    مستوى السرية: عالي جداً
    """.replace("{time}", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # كتابة الملف التجريبي
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(secret_content)
    
    print(f"📄 تم إنشاء الملف التجريبي: {test_file}")
    
    # تشفير الملف
    password = crypto.generate_secure_password(32)
    print(f"🔑 كلمة مرور مولدة تلقائياً: {password}")
    
    print("\n🔒 جاري تشفير الملف...")
    encrypted_file = crypto.encrypt_file(test_file, password=password)
    print(f"✅ تم تشفير الملف: {encrypted_file}")
    
    # فك تشفير الملف
    print("\n🔓 جاري فك تشفير الملف...")
    decrypted_file = crypto.decrypt_file(encrypted_file, password=password)
    print(f"✅ تم فك تشفير الملف: {decrypted_file}")
    
    # مقارنة المحتوى
    with open(test_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    with open(decrypted_file, 'r', encoding='utf-8') as f:
        decrypted_content = f.read()
    
    if original_content == decrypted_content:
        print("✅ التحقق من صحة تشفير الملف: نجح!")
    else:
        print("❌ التحقق من صحة تشفير الملف: فشل!")
    
    # تنظيف الملفات التجريبية
    import os
    for file in [test_file, encrypted_file, decrypted_file]:
        if os.path.exists(file):
            os.remove(file)
    print("🧹 تم حذف الملفات التجريبية")


def example_performance_benchmark():
    """مثال على قياس الأداء"""
    print("\n" + "=" * 60)
    print("قياس أداء خوارزمية التشفير")
    print("=" * 60)
    
    crypto = QuantumResistantCrypto()
    
    # اختبار أحجام مختلفة من البيانات
    test_sizes = [1024, 10*1024, 100*1024, 1024*1024]  # 1KB, 10KB, 100KB, 1MB
    
    for size in test_sizes:
        print(f"\n📊 اختبار الأداء لحجم البيانات: {size/1024:.1f} KB")
        
        try:
            benchmark_results = crypto.benchmark_encryption(size)
            
            print(f"   ⏱️  وقت التشفير: {benchmark_results['encryption_time_seconds']:.4f} ثانية")
            print(f"   ⏱️  وقت فك التشفير: {benchmark_results['decryption_time_seconds']:.4f} ثانية")
            print(f"   🚀 سرعة التشفير: {benchmark_results['encryption_speed_mbps']:.2f} MB/s")
            print(f"   🚀 سرعة فك التشفير: {benchmark_results['decryption_speed_mbps']:.2f} MB/s")
            print(f"   ⏰ الوقت الإجمالي: {benchmark_results['total_time_seconds']:.4f} ثانية")
            
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")


def example_security_features():
    """مثال على المميزات الأمنية"""
    print("\n" + "=" * 60)
    print("عرض المميزات الأمنية للخوارزمية")
    print("=" * 60)
    
    crypto = QuantumResistantCrypto()
    
    # البيانات التجريبية
    test_data = "بيانات تجريبية للاختبار".encode('utf-8')
    password = "test_password"
    
    # التشفير
    encrypted_package = crypto.encrypt_data(test_data, password)
    
    print("🔍 تحليل الحزمة المشفرة:")
    print(f"   📋 الإصدار: {encrypted_package['version']}")
    print(f"   🕐 الطابع الزمني: {time.ctime(encrypted_package['timestamp'])}")
    print(f"   🧂 الملح (Salt): {len(encrypted_package['salt'])} حرف base64")
    print(f"   🔢 المتجه الأولي (IV): {len(encrypted_package['iv'])} حرف base64")
    print(f"   🏷️  العلامة (Tag): {len(encrypted_package['tag'])} حرف base64")
    print(f"   🔐 النص المشفر: {len(encrypted_package['ciphertext'])} حرف base64")
    print(f"   ✅ hash السلامة: {len(encrypted_package['integrity_hash'])} حرف base64")
    print(f"   🛡️  الخوارزمية: {encrypted_package['algorithm']}")
    
    # اختبار مقاومة التلاعب
    print("\n🛡️ اختبار مقاومة التلاعب:")
    
    # محاولة تعديل البيانات المشفرة
    tampered_package = encrypted_package.copy()
    tampered_package['ciphertext'] = tampered_package['ciphertext'][:-1] + 'X'
    
    try:
        crypto.decrypt_data(tampered_package, password)
        print("   ❌ فشل في اكتشاف التلاعب!")
    except ValueError as e:
        print(f"   ✅ تم اكتشاف التلاعب بنجاح: {e}")
    
    # اختبار كلمة مرور خاطئة
    print("\n🔑 اختبار كلمة مرور خاطئة:")
    try:
        crypto.decrypt_data(encrypted_package, "wrong_password")
        print("   ❌ فشل في رفض كلمة المرور الخاطئة!")
    except ValueError as e:
        print(f"   ✅ تم رفض كلمة المرور الخاطئة بنجاح")


if __name__ == "__main__":
    print("🚀 تشغيل أمثلة خوارزمية التشفير المتقدمة")
    print("=" * 60)
    
    try:
        example_basic_encryption()
        example_rsa_hybrid_encryption()
        example_file_encryption()
        example_performance_benchmark()
        example_security_features()
        
        print("\n" + "=" * 60)
        print("✅ تم تشغيل جميع الأمثلة بنجاح!")
        print("🔐 خوارزمية التشفير جاهزة للاستخدام")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الأمثلة: {e}")
        import traceback
        traceback.print_exc()
