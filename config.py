#!/usr/bin/env python3
"""
ملف التكوين لخوارزمية التشفير المتقدمة
Configuration file for Advanced Quantum-Resistant Encryption Algorithm
"""

import os
from typing import Dict, Any

class CryptoConfig:
    """فئة إعدادات التشفير"""
    
    # إعدادات التشفير الافتراضية
    DEFAULT_KEY_SIZE = 32          # 256 bits
    DEFAULT_IV_SIZE = 16           # 128 bits  
    DEFAULT_SALT_SIZE = 32         # 256 bits
    DEFAULT_TAG_SIZE = 16          # 128 bits
    DEFAULT_RSA_KEY_SIZE = 4096    # RSA key size
    DEFAULT_PBKDF2_ITERATIONS = 100000  # PBKDF2 iterations
    
    # إعدادات كلمات المرور
    DEFAULT_PASSWORD_LENGTH = 32
    PASSWORD_CHARSET = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    # إعدادات الأداء
    DEFAULT_BENCHMARK_SIZE = 1024 * 1024  # 1MB
    MAX_FILE_SIZE = 100 * 1024 * 1024     # 100MB
    
    # إعدادات تطبيق الويب
    WEB_APP_HOST = "0.0.0.0"
    WEB_APP_PORT = 5000
    WEB_APP_DEBUG = True
    WEB_APP_SECRET_KEY = "quantum_resistant_crypto_secret_key_2024"
    
    # إعدادات الأمان
    SECURE_RANDOM_SOURCE = "system"  # system, urandom, secrets
    ENABLE_TIMING_ATTACK_PROTECTION = True
    ENABLE_MEMORY_PROTECTION = True
    
    # إعدادات التسجيل
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE = "crypto.log"
    
    # إعدادات الملفات المؤقتة
    TEMP_DIR = os.path.join(os.getcwd(), "temp")
    CLEANUP_TEMP_FILES = True
    
    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """الحصول على جميع الإعدادات"""
        return {
            "encryption": {
                "key_size": cls.DEFAULT_KEY_SIZE,
                "iv_size": cls.DEFAULT_IV_SIZE,
                "salt_size": cls.DEFAULT_SALT_SIZE,
                "tag_size": cls.DEFAULT_TAG_SIZE,
                "rsa_key_size": cls.DEFAULT_RSA_KEY_SIZE,
                "pbkdf2_iterations": cls.DEFAULT_PBKDF2_ITERATIONS,
            },
            "password": {
                "default_length": cls.DEFAULT_PASSWORD_LENGTH,
                "charset": cls.PASSWORD_CHARSET,
            },
            "performance": {
                "default_benchmark_size": cls.DEFAULT_BENCHMARK_SIZE,
                "max_file_size": cls.MAX_FILE_SIZE,
            },
            "web_app": {
                "host": cls.WEB_APP_HOST,
                "port": cls.WEB_APP_PORT,
                "debug": cls.WEB_APP_DEBUG,
                "secret_key": cls.WEB_APP_SECRET_KEY,
            },
            "security": {
                "random_source": cls.SECURE_RANDOM_SOURCE,
                "timing_attack_protection": cls.ENABLE_TIMING_ATTACK_PROTECTION,
                "memory_protection": cls.ENABLE_MEMORY_PROTECTION,
            },
            "logging": {
                "level": cls.LOG_LEVEL,
                "format": cls.LOG_FORMAT,
                "file": cls.LOG_FILE,
            },
            "temp": {
                "dir": cls.TEMP_DIR,
                "cleanup": cls.CLEANUP_TEMP_FILES,
            }
        }
    
    @classmethod
    def load_from_env(cls):
        """تحميل الإعدادات من متغيرات البيئة"""
        cls.DEFAULT_KEY_SIZE = int(os.getenv("CRYPTO_KEY_SIZE", cls.DEFAULT_KEY_SIZE))
        cls.DEFAULT_RSA_KEY_SIZE = int(os.getenv("CRYPTO_RSA_KEY_SIZE", cls.DEFAULT_RSA_KEY_SIZE))
        cls.DEFAULT_PBKDF2_ITERATIONS = int(os.getenv("CRYPTO_PBKDF2_ITERATIONS", cls.DEFAULT_PBKDF2_ITERATIONS))
        cls.DEFAULT_PASSWORD_LENGTH = int(os.getenv("CRYPTO_PASSWORD_LENGTH", cls.DEFAULT_PASSWORD_LENGTH))
        cls.WEB_APP_HOST = os.getenv("CRYPTO_WEB_HOST", cls.WEB_APP_HOST)
        cls.WEB_APP_PORT = int(os.getenv("CRYPTO_WEB_PORT", cls.WEB_APP_PORT))
        cls.WEB_APP_DEBUG = os.getenv("CRYPTO_WEB_DEBUG", "true").lower() == "true"
        cls.LOG_LEVEL = os.getenv("CRYPTO_LOG_LEVEL", cls.LOG_LEVEL)
        cls.TEMP_DIR = os.getenv("CRYPTO_TEMP_DIR", cls.TEMP_DIR)
    
    @classmethod
    def validate_config(cls) -> bool:
        """التحقق من صحة الإعدادات"""
        try:
            # التحقق من أحجام المفاتيح
            assert cls.DEFAULT_KEY_SIZE in [16, 24, 32], "حجم المفتاح يجب أن يكون 16, 24, أو 32 بايت"
            assert cls.DEFAULT_RSA_KEY_SIZE >= 2048, "حجم مفتاح RSA يجب أن يكون 2048 بت على الأقل"
            assert cls.DEFAULT_PBKDF2_ITERATIONS >= 10000, "عدد تكرارات PBKDF2 يجب أن يكون 10000 على الأقل"
            
            # التحقق من إعدادات كلمة المرور
            assert cls.DEFAULT_PASSWORD_LENGTH >= 8, "طول كلمة المرور يجب أن يكون 8 أحرف على الأقل"
            assert len(cls.PASSWORD_CHARSET) >= 64, "مجموعة أحرف كلمة المرور قصيرة جداً"
            
            # التحقق من إعدادات الشبكة
            assert 1 <= cls.WEB_APP_PORT <= 65535, "رقم المنفذ غير صحيح"
            
            return True
            
        except AssertionError as e:
            print(f"❌ خطأ في التحقق من الإعدادات: {e}")
            return False
        except Exception as e:
            print(f"❌ خطأ غير متوقع في التحقق من الإعدادات: {e}")
            return False
    
    @classmethod
    def create_temp_dir(cls):
        """إنشاء مجلد الملفات المؤقتة"""
        if not os.path.exists(cls.TEMP_DIR):
            os.makedirs(cls.TEMP_DIR)
    
    @classmethod
    def cleanup_temp_dir(cls):
        """تنظيف مجلد الملفات المؤقتة"""
        if cls.CLEANUP_TEMP_FILES and os.path.exists(cls.TEMP_DIR):
            import shutil
            try:
                shutil.rmtree(cls.TEMP_DIR)
                print(f"✅ تم تنظيف مجلد الملفات المؤقتة: {cls.TEMP_DIR}")
            except Exception as e:
                print(f"⚠️ تعذر تنظيف مجلد الملفات المؤقتة: {e}")


# تحميل الإعدادات من متغيرات البيئة عند استيراد الوحدة
CryptoConfig.load_from_env()

# التحقق من صحة الإعدادات
if not CryptoConfig.validate_config():
    print("⚠️ تحذير: بعض الإعدادات قد تكون غير صحيحة")

# إنشاء مجلد الملفات المؤقتة
CryptoConfig.create_temp_dir()

# تصدير الإعدادات للاستخدام
CONFIG = CryptoConfig.get_config()

if __name__ == "__main__":
    import json
    print("🔧 إعدادات خوارزمية التشفير المتقدمة:")
    print("=" * 50)
    print(json.dumps(CONFIG, indent=2, ensure_ascii=False))
    print("=" * 50)
    print("✅ جميع الإعدادات صحيحة!" if CryptoConfig.validate_config() else "❌ بعض الإعدادات غير صحيحة!")
