# Core dependencies
pycryptodomex>=3.18.0  # Cryptography functions
numpy>=1.21.0  # For numerical operations

# GPU acceleration (optional)
cupy-cuda11x>=10.0.0  # Replace with appropriate CUDA version
numba>=0.56.0  # For CUDA acceleration

# Password cracking
passlib>=1.7.4  # For password hashing and verification
bcrypt>=3.2.0  # For bcrypt hashing
scrypt>=0.8.20  # For scrypt key derivation

# Performance
joblib>=1.1.0  # For parallel processing
psutil>=5.9.0  # For system monitoring

# File format support
python-magic>=0.4.24  # For file type detection
python-magic-bin>=0.4.14; sys_platform == 'win32'  # Windows specific

# Hashing
hashlib-argon2>=21.2.0  # Argon2 hashing

# Command line interface
click>=8.0.0  # For CLI
rich>=10.0.0  # For beautiful console output

# Testing
pytest>=6.2.5
pytest-cov>=2.12.1

# Development
black>=21.12b0
flake8>=4.0.1
mypy>=0.930

# Documentation
sphinx>=4.3.2
sphinx-rtd-theme>=1.0.0
