#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for Universal Decryption Tool

This script creates test files with different encryption methods
and then attempts to decrypt them using the universal_decryptor.
"""

import os
import tempfile
import shutil
import unittest
import base64
from pathlib import Path
from Crypto.Cipher import AES, PKCS1_OAEP
from Crypto.PublicKey import RSA
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad, unpad
from universal_decryptor import UniversalDecryptor

# Test data
TEST_TEXT = "هذا اختبار لفك التشفير باستخدام الخوارزميات المختلفة"
TEST_PASSWORD = "كلمة_مرور_قوية_جدا"

class TestDecryptor(unittest.TestCase):    
    def setUp(self):
        """Set up test environment"""
        self.decryptor = UniversalDecryptor()
        self.test_dir = tempfile.mkdtemp(prefix="decrypt_test_")
        self.test_files = []
        
        # Generate test files
        self.create_test_files()
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def create_encrypted_file(self, algorithm, key=None, iv=None, mode='CBC'):
        """Helper to create encrypted test files"""
        filename = os.path.join(self.test_dir, f"test_{algorithm}_{mode.lower()}.enc")
        data = TEST_TEXT.encode('utf-8')
        
        if algorithm.upper() == 'AES':
            if not key:
                key = get_random_bytes(32)  # 256-bit key
            if not iv:
                iv = get_random_bytes(16)   # 128-bit IV
                
            if mode.upper() == 'CBC':
                cipher = AES.new(key, AES.MODE_CBC, iv)
                encrypted = cipher.encrypt(pad(data, AES.block_size))
            elif mode.upper() == 'GCM':
                cipher = AES.new(key, AES.MODE_GCM)
                encrypted, tag = cipher.encrypt_and_digest(data)
            
            with open(filename, 'wb') as f:
                if mode.upper() == 'GCM':
                    f.write(cipher.nonce)
                    f.write(tag)
                else:
                    f.write(iv)
                f.write(encrypted)
                
        elif algorithm.upper() == 'RSA':
            # Generate RSA key pair
            key = RSA.generate(2048)
            public_key = key.publickey()
            
            # Encrypt with RSA
            cipher = PKCS1_OAEP.new(public_key)
            encrypted = cipher.encrypt(data)
            
            with open(filename, 'wb') as f:
                f.write(encrypted)
            
            # Save private key for decryption test
            with open(filename + '.key', 'wb') as f:
                f.write(key.export_key())
        
        self.test_files.append(filename)
        return filename, key, iv
    
    def create_test_files(self):
        """Create various test files with different encryption"""
        # AES-256-CBC
        self.aes_cbc_file, self.aes_cbc_key, self.aes_cbc_iv = self.create_encrypted_file('AES', mode='CBC')
        
        # AES-256-GCM
        self.aes_gcm_file, self.aes_gcm_key, _ = self.create_encrypted_file('AES', mode='GCM')
        
        # RSA-2048
        self.rsa_file, self.rsa_key, _ = self.create_encrypted_file('RSA')
        
        # Create a simple XOR encrypted file
        xor_file = os.path.join(self.test_dir, "test_xor.enc")
        data = TEST_TEXT.encode('utf-8')
        key = TEST_PASSWORD.encode('utf-8')
        encrypted = bytes(b ^ key[i % len(key)] for i, b in enumerate(data))
        with open(xor_file, 'wb') as f:
            f.write(encrypted)
        self.test_files.append(xor_file)
        self.xor_file = xor_file
        self.xor_key = TEST_PASSWORD
    
    def test_aes_cbc_decryption(self):
        """Test AES-CBC decryption"""
        result = self.decryptor.analyze_file(self.aes_cbc_file)
        self.assertIn('aes', [a.lower() for a in result['possible_algorithms']])
        
        # Test decryption with known key
        decrypted = self.decryptor.decrypt_file(
            self.aes_cbc_file,
            algorithm='aes',
            key=base64.b64encode(self.aes_cbc_key).decode('utf-8'),
            iv=base64.b64encode(self.aes_cbc_iv).decode('utf-8'),
            mode='cbc'
        )
        self.assertEqual(decrypted['status'], 'success')
        self.assertEqual(decrypted['data'].decode('utf-8'), TEST_TEXT)
    
    def test_aes_gcm_decryption(self):
        """Test AES-GCM decryption"""
        result = self.decryptor.analyze_file(self.aes_gcm_file)
        self.assertIn('aes', [a.lower() for a in result['possible_algorithms']])
        
        # Test decryption with known key
        decrypted = self.decryptor.decrypt_file(
            self.aes_gcm_file,
            algorithm='aes',
            key=base64.b64encode(self.aes_gcm_key).decode('utf-8'),
            mode='gcm'
        )
        self.assertEqual(decrypted['status'], 'success')
        self.assertEqual(decrypted['data'].decode('utf-8'), TEST_TEXT)
    
    def test_rsa_decryption(self):
        """Test RSA decryption"""
        result = self.decryptor.analyze_file(self.rsa_file)
        self.assertIn('rsa', [a.lower() for a in result['possible_algorithms']])
        
        # Test decryption with private key
        with open(self.rsa_file + '.key', 'rb') as f:
            private_key = f.read().decode('utf-8')
        
        decrypted = self.decryptor.decrypt_file(
            self.rsa_file,
            algorithm='rsa',
            private_key=private_key
        )
        self.assertEqual(decrypted['status'], 'success')
        self.assertEqual(decrypted['data'].decode('utf-8'), TEST_TEXT)
    
    def test_xor_decryption(self):
        """Test XOR decryption"""
        result = self.decryptor.analyze_file(self.xor_file)
        self.assertIn('xor', [a.lower() for a in result['possible_algorithms']])
        
        # Test decryption with known key
        decrypted = self.decryptor.decrypt_file(
            self.xor_file,
            algorithm='xor',
            key=self.xor_key
        )
        self.assertEqual(decrypted['status'], 'success')
        self.assertEqual(decrypted['data'].decode('utf-8'), TEST_TEXT)

if __name__ == '__main__':
    unittest.main()
