# Universal File Decryptor

أداة فك التشفير الشاملة هي أداة متقدمة تمكنك من فك تشفير الملفات المشفرة باستخدام خوارزميات متعددة. تدعم الأداة مجموعة واسعة من خوارزميات التشفير وتقنيات الضغط.

## المميزات

- دعم تشفير متعدد الخوارزميات (AES, RSA, DES, 3DES, Blowfish, ChaCha20, RC4, إلخ.)
- كشف تلقائي لخوارزمية التشفير المستخدمة
- واجهة ويب سهلة الاستخدام
- دعم فك ضغط الملفات (ZIP, RAR, 7z, GZIP, BZIP2, LZMA)
- تحليل الملفات لاكتشاف التشفير
- واجهة سطر أوامر قوية

## المتطلبات

- Python 3.8 أو أحدث
- نظام تشغيل Windows/macOS/Linux
- اتصال بالإنترنت (لتحميل التبعيات)

## التثبيت

1. استنسخ المستودع:
   ```bash
   git clone [رابط المستودع]
   cd crypto
   ```

2. قم بإنشاء بيئة افتراضية (موصى به):
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   .\venv\Scripts\activate  # Windows
   ```

3. قم بتثبيت التبعيات:
   ```bash
   pip install -r requirements-decryptor.txt
   ```

## الاستخدام

### تشغيل واجهة الويب

```bash
python decryptor_web.py
```

ثم افتح المتصفح على العنوان:
```
http://localhost:5000
```

### استخدام سطر الأوامر

لتحليل ملف:
```bash
python universal_decryptor.py ملف_مشفر.enc
```

لفك تشفير ملف:
```bash
python universal_decryptor.py ملف_مشفر.enc -o ملف_مفكوك -p كلمة_المرور -a aes
```

## الخوارزميات المدعومة

- **التشفير المتماثل**:
  - AES (CBC, GCM, ECB, CFB, OFB, CTR)
  - DES (CBC, ECB, CFB, OFB)
  - 3DES (CBC, ECB)
  - Blowfish (CBC, ECB)
  - ChaCha20
  - RC4

- **التشفير غير المتماثل**:
  - RSA (PKCS#1 v1.5, OAEP)

- **الضغط والتغليف**:
  - ZIP (مع دعم كلمات المرور)
  - RAR (مع دعم كلمات المرور)
  - 7-Zip
  - GZIP
  - BZIP2
  - LZMA

## الأمان

- يتم معالجة كلمات المرور بشكل آمن باستخدام دوال التجزئة المشفرة
- لا يتم تخزين المفاتيح أو كلمات المرور على القرص
- اتصالات HTTPS مشفرة عند استخدام واجهة الويب
- حماية من هجمات التخمين بكلمات المرور

## المساهمة

نرحب بالمساهمات! يرجى قراءة [إرشادات المساهمة](CONTRIBUTING.md) للحصول على تفاصيل حول كيفية المساهمة في المشروع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

لأية استفسارات أو مشاكل، يرجى فتح [issue](https://github.com/yourusername/crypto/issues) جديد.
