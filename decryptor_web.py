#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Universal File Decryptor Web Interface

واجهة ويب متكاملة لأداة فك التشفير الشاملة
"""

import os
import sys
import json
import base64
import logging
from pathlib import Path
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
from werkzeug.utils import secure_filename
from universal_decryptor import UniversalDecryptor

# Configure Flask app
app = Flask(__name__)
app.secret_key = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['DECRYPTED_FOLDER'] = 'decrypted'
app.config['ALLOWED_EXTENSIONS'] = {'bin', 'enc', 'crypt', 'aes', 'des', '3des', 'rsa', 'gpg', 'pgp', '7z', 'zip', 'rar', 'gz', 'bz2', 'xz'}

# Ensure upload and decrypted directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['DECRYPTED_FOLDER'], exist_ok=True)

# Initialize the decryptor
decryptor = UniversalDecryptor()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('decryptor_web.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def allowed_file(filename):
    """Check if the file has an allowed extension"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

@app.route('/')
def index():
    """Render the main page"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """
    Handle file upload and initial analysis
    """
    # Check if the post request has the file part
    if 'file' not in request.files:
        flash('لم يتم اختيار ملف', 'error')
        return redirect(request.url)
    
    file = request.files['file']
    
    # If user does not select file, browser also
    # submit an empty part without filename
    if file.filename == '':
        flash('لم يتم اختيار ملف', 'error')
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Analyze the file
        try:
            analysis = decryptor.analyze_file(filepath)
            
            # Save analysis to session or database (simplified)
            session_id = str(hash(filepath))
            session_file = os.path.join(app.config['UPLOAD_FOLDER'], f"{session_id}.json")
            
            with open(session_file, 'w') as f:
                json.dump({
                    'original_filename': filename,
                    'filepath': filepath,
                    'analysis': analysis,
                    'upload_time': datetime.now().isoformat(),
                    'status': 'uploaded'
                }, f)
            
            return jsonify({
                'status': 'success',
                'session_id': session_id,
                'analysis': analysis
            })
            
        except Exception as e:
            logger.error(f"Error analyzing file: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'خطأ في تحليل الملف: {str(e)}'
            }), 500
    
    return jsonify({
        'status': 'error',
        'message': 'نوع الملف غير مدعوم'
    }), 400

@app.route('/decrypt', methods=['POST'])
def decrypt():
    """Handle decryption request"""
    data = request.get_json()
    session_id = data.get('session_id')
    password = data.get('password')
    algorithm = data.get('algorithm')
    
    if not session_id:
        return jsonify({
            'status': 'error',
            'message': 'معرف الجلسة غير صالح'
        }), 400
    
    session_file = os.path.join(app.config['UPLOAD_FOLDER'], f"{session_id}.json")
    
    if not os.path.exists(session_file):
        return jsonify({
            'status': 'error',
            'message': 'انتهت صلاحية الجلسة أو غير موجودة'
        }), 404
    
    try:
        with open(session_file, 'r') as f:
            session_data = json.load(f)
        
        filepath = session_data['filepath']
        output_filename = f"decrypted_{os.path.basename(filepath).rsplit('.', 1)[0]}"
        output_path = os.path.join(app.config['DECRYPTED_FOLDER'], output_filename)
        
        # Try to decrypt the file
        success = decryptor.decrypt_file(
            filepath,
            output_path=output_path,
            password=password,
            algorithm=algorithm
        )
        
        if success:
            # Update session data
            session_data['status'] = 'decrypted'
            session_data['decrypted_path'] = output_path
            session_data['decryption_time'] = datetime.now().isoformat()
            
            with open(session_file, 'w') as f:
                json.dump(session_data, f)
            
            return jsonify({
                'status': 'success',
                'message': 'تم فك التشفير بنجاح',
                'download_url': f'/download/{os.path.basename(output_path)}'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'فشل فك التشفير. يرجى التحقق من كلمة المرور أو خوارزمية التشفير.'
            }), 400
            
    except Exception as e:
        logger.error(f"Decryption error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'خطأ في فك التشفير: {str(e)}'
        }), 500

@app.route('/download/<filename>')
def download_file(filename):
    """Serve decrypted files for download"""
    return send_from_directory(
        app.config['DECRYPTED_FOLDER'],
        filename,
        as_attachment=True
    )

@app.route('/api/analyze', methods=['POST'])
def api_analyze():
    """API endpoint for file analysis"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], f"temp_{filename}")
        file.save(filepath)
        
        try:
            analysis = decryptor.analyze_file(filepath)
            os.remove(filepath)  # Clean up temp file
            return jsonify(analysis)
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    return jsonify({'error': 'File type not allowed'}), 400

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
